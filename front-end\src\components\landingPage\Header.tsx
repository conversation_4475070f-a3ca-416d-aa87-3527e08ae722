import React, { useState } from 'react';
import { FaPhone, FaEnvelope, FaBars } from 'react-icons/fa';

interface HeaderProps {
  // Vous pouvez ajouter des props personnalisées ici si nécessaire
}

const Header: React.FC<HeaderProps> = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header className="w-full">
      {/* Top Bar - Horaires et urgence */}
      <div className="bg-gray-500 text-white py-2 text-center text-sm">
        <p>Mon – Sat: 7:00 – 18:00 · Sunday: Closed · Emergency: 24hr / 7days</p>
      </div>

      {/* Main Header - Logo et Contact */}
      <div className="bg-white py-4 shadow-sm">
        <div className="container mx-auto px-4 flex flex-col md:flex-row justify-between items-center">
          {/* Logo */}
          <div className="mb-4 md:mb-0">
            <a href="/" className="flex items-center">
              <img
                src="/src/assets/logo.svg"
                alt="Construction & Renovation"
                className="h-12"
              />
            </a>
          </div>

          {/* Contact Info */}
          <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-8">
            {/* Call Us Now */}
            <div className="flex items-center">
              <div className="mr-3 bg-gray-100 p-3 rounded-full">
                <FaPhone className="text-gray-400 text-xl" />
              </div>
              <div>
                <p className="text-gray-500 text-xs">CALL US NOW:</p>
                <a href="tel:************" className="text-[#5b99c2] font-semibold">************</a>
              </div>
            </div>

            {/* Quote Request */}
            <div className="flex items-center">
              <div className="mr-3 bg-gray-100 p-3 rounded-full">
                <FaEnvelope className="text-gray-400 text-xl" />
              </div>
              <div>
                <p className="text-gray-500 text-xs">WANT AN APPROXIMATE PRICE?</p>
                <a href="/quote" className="text-[#5b99c2] font-semibold">GET A FREE QUOTE »</a>
              </div>
            </div>

            {/* Call Us Now (Duplicate) */}
            <div className="flex items-center">
              <div className="mr-3 bg-gray-100 p-3 rounded-full">
                <FaPhone className="text-gray-400 text-xl" />
              </div>
              <div>
                <p className="text-gray-500 text-xs">CALL US NOW:</p>
                <a href="tel:************" className="text-[#5b99c2] font-semibold">************</a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="bg-blue-100 border-t border-gray-200">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            {/* Desktop Navigation */}
            <div className="hidden md:flex">
              <ul className="flex">
                <li>
                  <a href="/" className="block py-4 px-4 text-[#5b99c2] font-medium border-b-2 border-[#5b99c2]">HOME</a>
                </li>
                <li>
                  <a href="/services" className="block py-4 px-4 text-gray-700 hover:text-[#5b99c2]">SERVICES</a>
                </li>
                <li>
                  <a href="/projects" className="block py-4 px-4 text-gray-700 hover:text-[#5b99c2]">PROJECTS</a>
                </li>
                <li>
                  <a href="/about" className="block py-4 px-4 text-gray-700 hover:text-[#5b99c2]">ABOUT</a>
                </li>
                <li>
                  <a href="/news" className="block py-4 px-4 text-gray-700 hover:text-[#5b99c2]">NEWS</a>
                </li>
                <li>
                  <a href="/shop" className="block py-4 px-4 text-gray-700 hover:text-[#5b99c2]">SHOP</a>
                </li>
                <li>
                  <a href="/features" className="block py-4 px-4 text-gray-700 hover:text-[#5b99c2]">FEATURES</a>
                </li>
                <li>
                  <a href="/contacts" className="block py-4 px-4 text-gray-700 hover:text-[#5b99c2]">CONTACTS</a>
                </li>
              </ul>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <button
                onClick={toggleMobileMenu}
                className="p-2 focus:outline-none"
              >
                <FaBars className="text-gray-700 text-xl" />
              </button>
            </div>

            {/* Account Links */}
            <div className="hidden md:flex">
              <div className="flex items-center">
                <a href="/signup" className="px-2 text-gray-700 hover:text-[#5b99c2] text-md">S'inscrire</a>
                <span className="text-gray-400">|</span>
                <a href="/signin" className="px-2 text-gray-700 hover:text-[#5b99c2] text-md">Se connecter</a>
              </div>
            </div>
          </div>

          {/* Mobile Navigation */}
          {mobileMenuOpen && (
            <div className="md:hidden">
              <ul className="border-t border-gray-200 py-2">
                <li>
                  <a href="/" className="block py-2 px-4 text-[#5b99c2] font-medium">HOME</a>
                </li>
                <li>
                  <a href="/services" className="block py-2 px-4 text-gray-700">SERVICES</a>
                </li>
                <li>
                  <a href="/projects" className="block py-2 px-4 text-gray-700">PROJECTS</a>
                </li>
                <li>
                  <a href="/about" className="block py-2 px-4 text-gray-700">ABOUT</a>
                </li>
                <li>
                  <a href="/news" className="block py-2 px-4 text-gray-700">NEWS</a>
                </li>
                <li>
                  <a href="/shop" className="block py-2 px-4 text-gray-700">SHOP</a>
                </li>
                <li>
                  <a href="/features" className="block py-2 px-4 text-gray-700">FEATURES</a>
                </li>
                <li>
                  <a href="/contacts" className="block py-2 px-4 text-gray-700">CONTACTS</a>
                </li>
                <li className="border-t border-gray-200 mt-2 pt-2">
                  <div className="flex items-center border border-gray-200 rounded-md px-2 py-1 mx-4 my-2 transition-all duration-300 hover:border-[#5b99c2] hover:shadow-sm w-fit">
                    <a href="/signup" className="px-2 text-gray-700 hover:text-[#5b99c2]">S'inscrire</a>
                    <span className="text-gray-400">|</span>
                    <a href="/signin" className="px-2 text-gray-700 hover:text-[#5b99c2]">Se connecter</a>
                  </div>
                </li>
              </ul>
            </div>
          )}
        </div>
      </nav>
    </header>
  );
};

export default Header;