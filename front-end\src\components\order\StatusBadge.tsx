

interface Props {
    status: string;
}



const StatusBadge = ({ status }: Props) => {
  const statusStyles: { [key: string]: string } = {
    "Complète": "bg-emerald-100 text-emerald-600",
    "Incomplète": "bg-red-100 text-red-600",
    "En attente": "bg-[#5b99c2]/20 text-[#5b99c2]",
  };

  return (
    <span
      className={`px-3 py-1 rounded-full text-sm ${
        statusStyles[status] || "bg-gray-100 text-gray-600"
      }`}
    >
      {status}
    </span>
  );
};


export default StatusBadge;