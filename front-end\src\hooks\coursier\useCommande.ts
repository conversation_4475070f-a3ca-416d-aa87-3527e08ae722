import { useQuery } from "@tanstack/react-query";
import { Commande } from "../../models/coursier/commande";
import apiClient from "../../services/api-client";

const COMMANDES_PATH = "/commandes";

const useCommande = (CommandeId: string) => useQuery<Commande, Error>({
    queryKey: ["commande", CommandeId],
    queryFn: () => apiClient
                        .get<Commande>(`${COMMANDES_PATH}/${CommandeId}`)
                        .then((response) => response.data),
});


export default useCommande;