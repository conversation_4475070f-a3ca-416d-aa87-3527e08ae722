

interface Props {
    active: boolean;
    children: React.ReactNode;
    onClick: () => void;
}



const PaginationButton = ({ active, children, onClick }: Props) => (
<button
    onClick={onClick}
    className={`px-3 py-2 rounded-md transition-colors duration-200 ${
    active ? "bg-blue-500 text-white" : "text-gray-600 hover:bg-gray-100"
    }`}
>
    {children}
</button>
);


export default PaginationButton;