import React, { useState, useEffect } from "react";
import { information } from "../../models/domicialisation/information";

// The component
interface RegisterInfoProps {
    onSave: (data: Partial<information>) => void;
    formData: Partial<information>; // To populate form when user revisits the step
}

export const RegisterInfo: React.FC<RegisterInfoProps> = ({ onSave, formData }) => {
    const [localData, setLocalData] = useState<Partial<information>>(formData);

    // Sync formData prop when it changes (for when you revisit this step)
    useEffect(() => {
        setLocalData(formData);
    }, [formData]);

    // Function to handle input changes
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setLocalData((prevData) => ({
            ...prevData,
            [name]: value, // Dynamically update the field based on input's name
        }));
    };

    // Save data when navigating to the next step
    useEffect(() => {
        onSave(localData);
    }, [localData, onSave]);

    return (
        <div>
            <label className="form-control items-center justify-center flex flex-row w-full mx-5">
                <div className={"flex flex-col mx-auto w-1/2"}>
                    <div className="label">
                        <span className="label-text">First Name?</span>
                    </div>
                    <input
                        type="text"
                        name="firstName"
                        value={localData.firstName || ""}
                        onChange={handleChange}
                        placeholder=""
                        className="input input-bordered w-full max-w-xs"
                    />
                </div>

                <div className={"flex flex-col mx-auto ml-5 w-1/2"}>
                    <div className="label">
                        <span className="label-text">Last Name?</span>
                    </div>
                    <input
                        type="text"
                        name="lastName"
                        value={localData.lastName || ""}
                        onChange={handleChange}
                        placeholder=""
                        className="input input-bordered w-full max-w-xs"
                    />
                </div>
            </label>

            <label className="form-control items-center justify-center flex flex-row w-full mx-5">
                <div className={"flex flex-col mx-auto w-1/2"}>
                    <div className="label">
                        <span className="label-text">Nom d'entreprise</span>
                    </div>
                    <input
                        type="text"
                        name="Nom_entreprise"
                        value={localData.Nom_entreprise || ""}
                        onChange={handleChange}
                        placeholder=""
                        className="input input-bordered w-full max-w-xs"
                    />
                </div>

                <div className={"flex flex-col mx-auto ml-5 w-1/2"}>
                    <div className="label">
                        <span className="label-text">Address</span>
                    </div>
                    <input
                        type="text"
                        name="address"
                        value={localData.address || ""}
                        onChange={handleChange}
                        placeholder=""
                        className="input input-bordered w-full max-w-xs"
                    />
                </div>
            </label>

            <label className="form-control w-full mx-5">
                <div className="label">
                    <span className="label-text">Ville</span>
                </div>
                <input
                    type="text"
                    name="ville"
                    value={localData.ville || ""}
                    onChange={handleChange}
                    placeholder=""
                    className="input input-bordered w-full max-w-xs"
                />
            </label>
        </div>
    );
};
