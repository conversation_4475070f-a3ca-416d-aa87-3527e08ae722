import React from 'react';
import {
  <PERSON>rid,
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  useTheme,
  alpha,
} from '@mui/material';
import {
  IconPackage,
  IconTruck,
  IconClock,
  IconCheck,
  IconPlus,
} from '@tabler/icons-react';
import PageContainer from 'src/components/container/PageContainer';
import DashboardCard from 'src/components/shared/DashboardCard';
import { useAuthContext } from 'src/context/AuthContext';

const ClientDashboard = () => {
  const theme = useTheme();
  const { user } = useAuthContext();

  // Données simulées - à remplacer par de vraies données API
  const stats = [
    {
      title: 'Commandes en cours',
      value: '3',
      icon: IconClock,
      color: theme.palette.warning.main,
      bgColor: alpha(theme.palette.warning.main, 0.1),
    },
    {
      title: 'Commandes livrées',
      value: '24',
      icon: IconCheck,
      color: theme.palette.success.main,
      bgColor: alpha(theme.palette.success.main, 0.1),
    },
    {
      title: 'Total commandes',
      value: '27',
      icon: IconPackage,
      color: theme.palette.primary.main,
      bgColor: alpha(theme.palette.primary.main, 0.1),
    },
    {
      title: 'Coursiers actifs',
      value: '12',
      icon: IconTruck,
      color: theme.palette.secondary.main,
      bgColor: alpha(theme.palette.secondary.main, 0.1),
    },
  ];

  const recentOrders = [
    {
      id: 'CMD001',
      destination: 'Casablanca Centre',
      status: 'En cours',
      date: '2024-01-15',
      coursier: 'Ahmed Benali',
    },
    {
      id: 'CMD002',
      destination: 'Rabat Agdal',
      status: 'Livré',
      date: '2024-01-14',
      coursier: 'Fatima Zahra',
    },
    {
      id: 'CMD003',
      destination: 'Marrakech Gueliz',
      status: 'En préparation',
      date: '2024-01-13',
      coursier: 'Omar Idrissi',
    },
  ];

  const services = [
    {
      title: 'Nouvelle livraison',
      description: 'Créer une nouvelle commande de livraison',
      icon: IconPlus,
      action: 'Créer',
      color: theme.palette.primary.main,
    },
    {
      title: 'Suivi des colis',
      description: 'Suivre vos colis en temps réel',
      icon: IconPackage,
      action: 'Suivre',
      color: theme.palette.secondary.main,
    },
    {
      title: 'Historique',
      description: 'Consulter l\'historique de vos commandes',
      icon: IconClock,
      action: 'Voir',
      color: theme.palette.info.main,
    },
  ];

  return (
    <PageContainer title="Dashboard Client" description="Tableau de bord client">
      <Box>
        {/* Welcome Section */}
        <DashboardCard>
          <Box sx={{ mb: 4 }}>
            <Typography variant="h4" gutterBottom>
              Bienvenue, {user?.name} !
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Gérez vos livraisons et suivez vos commandes depuis votre tableau de bord.
            </Typography>
          </Box>
        </DashboardCard>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                sx={{
                  height: '100%',
                  background: `linear-gradient(135deg, ${stat.bgColor} 0%, ${alpha(stat.color, 0.05)} 100%)`,
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box
                      sx={{
                        p: 1,
                        borderRadius: 2,
                        bgcolor: stat.color,
                        color: 'white',
                        mr: 2,
                      }}
                    >
                      <stat.icon size={24} />
                    </Box>
                    <Typography variant="h4" fontWeight="bold">
                      {stat.value}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="textSecondary">
                    {stat.title}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={3}>
          {/* Services rapides */}
          <Grid item xs={12} md={6}>
            <DashboardCard title="Services rapides">
              <Grid container spacing={2}>
                {services.map((service, index) => (
                  <Grid item xs={12} key={index}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: theme.shadows[4],
                        },
                      }}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box
                              sx={{
                                p: 1,
                                borderRadius: 2,
                                bgcolor: alpha(service.color, 0.1),
                                color: service.color,
                                mr: 2,
                              }}
                            >
                              <service.icon size={20} />
                            </Box>
                            <Box>
                              <Typography variant="subtitle1" fontWeight="600">
                                {service.title}
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                {service.description}
                              </Typography>
                            </Box>
                          </Box>
                          <Button
                            variant="outlined"
                            size="small"
                            sx={{ color: service.color, borderColor: service.color }}
                          >
                            {service.action}
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </DashboardCard>
          </Grid>

          {/* Commandes récentes */}
          <Grid item xs={12} md={6}>
            <DashboardCard title="Commandes récentes">
              <Box>
                {recentOrders.map((order, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      py: 2,
                      borderBottom: index < recentOrders.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                    }}
                  >
                    <Box>
                      <Typography variant="subtitle2" fontWeight="600">
                        {order.id}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {order.destination}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        Coursier: {order.coursier}
                      </Typography>
                    </Box>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography
                        variant="caption"
                        sx={{
                          px: 1,
                          py: 0.5,
                          borderRadius: 1,
                          bgcolor: order.status === 'Livré' 
                            ? alpha(theme.palette.success.main, 0.1)
                            : order.status === 'En cours'
                            ? alpha(theme.palette.warning.main, 0.1)
                            : alpha(theme.palette.info.main, 0.1),
                          color: order.status === 'Livré' 
                            ? theme.palette.success.main
                            : order.status === 'En cours'
                            ? theme.palette.warning.main
                            : theme.palette.info.main,
                        }}
                      >
                        {order.status}
                      </Typography>
                      <Typography variant="caption" display="block" color="textSecondary" sx={{ mt: 0.5 }}>
                        {order.date}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Button variant="text" size="small">
                  Voir toutes les commandes
                </Button>
              </Box>
            </DashboardCard>
          </Grid>
        </Grid>
      </Box>
    </PageContainer>
  );
};

export default ClientDashboard;
