import { useState } from 'react';
import { FaBars } from "react-icons/fa6";
import { FaRegCircleXmark } from "react-icons/fa6";
import {Link} from "react-router-dom";



function NavBar() {
    const [isOpen, setIsOpen] = useState(false);

    const toggleNavbar = () => {
        setIsOpen(prev => !prev);
    };

    return (
        <div className="navbar bg-primaryColor text-black shadow-custom-shadow px-8 py-4 flex items-center justify-between">
            {/* Logo */}
            <a href="/">
                <img className="w-[151.44px] h-[46.966px]" src="/src/assets/logo.svg" alt="Charikti" />
            </a>

            {/* Toggle button for mobile */}
            <button onClick={toggleNavbar} className="md:hidden text-black p-2">
                {isOpen ? <FaRegCircleXmark /> : <FaBars />}
            </button>

            {/* Menu items */}
            <div className={`flex items-center gap-[10px] ${isOpen ? 'block' : 'hidden'} md:flex`}>
                <ul className="menu md:menu-horizontal menu-vertical px-1">
                    <li><a href="#">About</a></li>
                    <li><a href="#">Blog</a></li>
                    <li>
                        <Link to="signin">Sign In</Link>
                    </li>
                    <li>
                        <Link to={"signup"} className="flex  h-[42px] p-[10px_25px] justify-between items-center flex-shrink-0 rounded-[6px] bg-[#184970] text-white">
                            <span>Sign Up</span>
                        </Link>
                    </li>
                    <li>
                        <details>
                            <summary>Langue</summary>
                            <ul style={{ zIndex: 10 }} className="bg-primaryColor border rounded-t-none p-2">
                                <li><a href="src/components/landingPage/NavBar#">French</a></li>
                                <li><a href="src/components/landingPage/NavBar#">English</a></li>
                            </ul>
                        </details>
                    </li>
                </ul>
            </div>
        </div>
    );
}

export default NavBar;
