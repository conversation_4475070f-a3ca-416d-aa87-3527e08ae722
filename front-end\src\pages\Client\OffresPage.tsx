import React from 'react';

const OffresPage = () => {
  // Exemple d'offres
  const offres = [
    {
      id: 1,
      titre: "Pack Démarrage",
      description: "Idéal pour les entrepreneurs individuels et les petites entreprises.",
      prix: 1500,
      caracteristiques: [
        "Domiciliation pour 1 an",
        "Réception du courrier",
        "Notification par email",
        "Accès à l'espace client"
      ]
    },
    {
      id: 2,
      titre: "Pack Business",
      description: "Solution complète pour les PME et entreprises en croissance.",
      prix: 2500,
      caracteristiques: [
        "Domiciliation pour 1 an",
        "Réception et numérisation du courrier",
        "Notification par email et SMS",
        "Accès à l'espace client",
        "Salle de réunion (5h/mois)"
      ]
    },
    {
      id: 3,
      titre: "Pack Premium",
      description: "Service haut de gamme pour les entreprises établies.",
      prix: 4000,
      caracteristiques: [
        "Domiciliation pour 1 an",
        "Réception, numérisation et transfert du courrier",
        "Notification par email et SMS",
        "Accès à l'espace client premium",
        "Salle de réunion (10h/mois)",
        "Assistance juridique"
      ]
    }
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Nos Offres</h1>
      <p className="mb-6">Découvrez nos offres de domiciliation et services pour votre entreprise.</p>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {offres.map(offre => (
          <div key={offre.id} className="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl transition-shadow duration-300">
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-2">{offre.titre}</h2>
              <p className="text-gray-600 mb-4">{offre.description}</p>
              <p className="text-3xl font-bold text-blue-600 mb-6">{offre.prix} DH<span className="text-sm text-gray-500 font-normal">/an</span></p>
              
              <ul className="space-y-2 mb-6">
                {offre.caracteristiques.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
              
              <button className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-lg transition-colors duration-200">
                Souscrire
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default OffresPage;
