import React from 'react';
import {
  Grid,
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  useTheme,
  alpha,
  LinearProgress,
} from '@mui/material';
import {
  IconBuildingStore,
  IconFileText,
  IconCalculator,
  IconMapPin,
  IconUsers,
  IconTrendingUp,
  IconClipboardList,
  IconShield,
} from '@tabler/icons-react';
import PageContainer from 'src/components/container/PageContainer';
import DashboardCard from 'src/components/shared/DashboardCard';
import { useAuthContext } from 'src/context/AuthContext';

const EntrepriseDashboard = () => {
  const theme = useTheme();
  const { user } = useAuthContext();

  // Données simulées - à remplacer par de vraies données API
  const stats = [
    {
      title: 'Dossiers actifs',
      value: '12',
      icon: IconFileText,
      color: theme.palette.primary.main,
      bgColor: alpha(theme.palette.primary.main, 0.1),
    },
    {
      title: 'Services utilisés',
      value: '5',
      icon: IconClipboardList,
      color: theme.palette.secondary.main,
      bgColor: alpha(theme.palette.secondary.main, 0.1),
    },
    {
      title: 'Domiciliations',
      value: '3',
      icon: IconMapPin,
      color: theme.palette.success.main,
      bgColor: alpha(theme.palette.success.main, 0.1),
    },
    {
      title: 'Économies réalisées',
      value: '15,000 DH',
      icon: IconTrendingUp,
      color: theme.palette.warning.main,
      bgColor: alpha(theme.palette.warning.main, 0.1),
    },
  ];

  const services = [
    {
      title: 'Comptabilité',
      description: 'Gestion comptable complète',
      icon: IconCalculator,
      progress: 75,
      status: 'En cours',
      color: theme.palette.primary.main,
    },
    {
      title: 'Domiciliation',
      description: 'Adresse commerciale et siège social',
      icon: IconMapPin,
      progress: 100,
      status: 'Actif',
      color: theme.palette.success.main,
    },
    {
      title: 'Juridique',
      description: 'Conseils et formalités juridiques',
      icon: IconShield,
      progress: 50,
      status: 'En attente',
      color: theme.palette.warning.main,
    },
    {
      title: 'Ressources Humaines',
      description: 'Gestion du personnel',
      icon: IconUsers,
      progress: 25,
      status: 'Planifié',
      color: theme.palette.info.main,
    },
  ];

  const recentDocuments = [
    {
      name: 'Bilan comptable 2024',
      type: 'Comptabilité',
      date: '2024-01-15',
      status: 'Prêt',
    },
    {
      name: 'Déclaration TVA Q1',
      type: 'Fiscal',
      date: '2024-01-10',
      status: 'En cours',
    },
    {
      name: 'Contrat domiciliation',
      type: 'Juridique',
      date: '2024-01-08',
      status: 'Signé',
    },
    {
      name: 'Attestation CNSS',
      type: 'Social',
      date: '2024-01-05',
      status: 'Validé',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Prêt':
      case 'Signé':
      case 'Validé':
      case 'Actif':
        return theme.palette.success.main;
      case 'En cours':
        return theme.palette.warning.main;
      case 'En attente':
        return theme.palette.info.main;
      case 'Planifié':
        return theme.palette.secondary.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  return (
    <PageContainer title="Dashboard Entreprise" description="Tableau de bord entreprise">
      <Box>
        {/* Welcome Section */}
        <DashboardCard>
          <Box sx={{ mb: 4 }}>
            <Typography variant="h4" gutterBottom>
              Bienvenue, {user?.name} !
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Gérez tous vos services d'entreprise depuis votre tableau de bord centralisé.
            </Typography>
          </Box>
        </DashboardCard>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                sx={{
                  height: '100%',
                  background: `linear-gradient(135deg, ${stat.bgColor} 0%, ${alpha(stat.color, 0.05)} 100%)`,
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box
                      sx={{
                        p: 1,
                        borderRadius: 2,
                        bgcolor: stat.color,
                        color: 'white',
                        mr: 2,
                      }}
                    >
                      <stat.icon size={24} />
                    </Box>
                    <Typography variant="h4" fontWeight="bold">
                      {stat.value}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="textSecondary">
                    {stat.title}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={3}>
          {/* Services */}
          <Grid item xs={12} md={8}>
            <DashboardCard title="Mes Services">
              <Grid container spacing={2}>
                {services.map((service, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Card
                      sx={{
                        height: '100%',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: theme.shadows[4],
                        },
                      }}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Box
                            sx={{
                              p: 1,
                              borderRadius: 2,
                              bgcolor: alpha(service.color, 0.1),
                              color: service.color,
                              mr: 2,
                            }}
                          >
                            <service.icon size={24} />
                          </Box>
                          <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="h6" fontWeight="600">
                              {service.title}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              {service.description}
                            </Typography>
                          </Box>
                        </Box>
                        
                        <Box sx={{ mb: 2 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2" color="textSecondary">
                              Progression
                            </Typography>
                            <Typography variant="body2" fontWeight="600">
                              {service.progress}%
                            </Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={service.progress}
                            sx={{
                              height: 6,
                              borderRadius: 3,
                              bgcolor: alpha(service.color, 0.1),
                              '& .MuiLinearProgress-bar': {
                                bgcolor: service.color,
                                borderRadius: 3,
                              },
                            }}
                          />
                        </Box>
                        
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography
                            variant="caption"
                            sx={{
                              px: 1,
                              py: 0.5,
                              borderRadius: 1,
                              bgcolor: alpha(getStatusColor(service.status), 0.1),
                              color: getStatusColor(service.status),
                              fontWeight: 600,
                            }}
                          >
                            {service.status}
                          </Typography>
                          <Button variant="text" size="small">
                            Voir détails
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </DashboardCard>
          </Grid>

          {/* Documents récents */}
          <Grid item xs={12} md={4}>
            <DashboardCard title="Documents récents">
              <Box>
                {recentDocuments.map((doc, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      py: 2,
                      borderBottom: index < recentDocuments.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                    }}
                  >
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="subtitle2" fontWeight="600">
                        {doc.name}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {doc.type}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {doc.date}
                      </Typography>
                    </Box>
                    <Typography
                      variant="caption"
                      sx={{
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        bgcolor: alpha(getStatusColor(doc.status), 0.1),
                        color: getStatusColor(doc.status),
                        fontWeight: 600,
                        ml: 1,
                      }}
                    >
                      {doc.status}
                    </Typography>
                  </Box>
                ))}
              </Box>
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Button variant="text" size="small">
                  Voir tous les documents
                </Button>
              </Box>
            </DashboardCard>
          </Grid>
        </Grid>

        {/* Actions rapides */}
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12}>
            <DashboardCard title="Actions rapides">
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<IconFileText />}
                    sx={{ py: 2 }}
                  >
                    Nouveau document
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<IconCalculator />}
                    sx={{ py: 2 }}
                  >
                    Demande comptable
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<IconMapPin />}
                    sx={{ py: 2 }}
                  >
                    Domiciliation
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<IconShield />}
                    sx={{ py: 2 }}
                  >
                    Conseil juridique
                  </Button>
                </Grid>
              </Grid>
            </DashboardCard>
          </Grid>
        </Grid>
      </Box>
    </PageContainer>
  );
};

export default EntrepriseDashboard;
