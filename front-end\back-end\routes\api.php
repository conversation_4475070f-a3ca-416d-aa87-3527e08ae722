<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\ClientController;
use App\Http\Controllers\API\CoursierController;
use App\Http\Controllers\API\EntrepriseController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Public profile creation routes
Route::post('/clients', [ClientController::class, 'store']);
Route::post('/coursiers', [CoursierController::class, 'store']);
Route::post('/entreprises', [EntrepriseController::class, 'store']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/change-password', [AuthController::class, 'changePassword']);

    // Client routes
    Route::prefix('clients')->group(function () {
        Route::get('/', [ClientController::class, 'index']);
        Route::post('/', [ClientController::class, 'store']);
        Route::get('/profile', [ClientController::class, 'profile']);
        Route::get('/{client}', [ClientController::class, 'show']);
        Route::put('/{client}', [ClientController::class, 'update']);
        Route::post('/{client}/profile-image', [ClientController::class, 'updateProfileImage']);
        Route::delete('/{client}', [ClientController::class, 'destroy']);
    });

    // Coursier routes
    Route::prefix('coursiers')->group(function () {
        Route::get('/', [CoursierController::class, 'index']);
        Route::post('/', [CoursierController::class, 'store']);
        Route::get('/profile', [CoursierController::class, 'profile']);
        Route::get('/{coursier}', [CoursierController::class, 'show']);
        Route::put('/{coursier}', [CoursierController::class, 'update']);
        Route::post('/{coursier}/profile-image', [CoursierController::class, 'updateProfileImage']);
        Route::delete('/{coursier}', [CoursierController::class, 'destroy']);
    });

    // Entreprise routes
    Route::prefix('entreprises')->group(function () {
        Route::get('/', [EntrepriseController::class, 'index']);
        Route::post('/', [EntrepriseController::class, 'store']);
        Route::get('/profile', [EntrepriseController::class, 'profile']);
        Route::get('/{entreprise}', [EntrepriseController::class, 'show']);
        Route::put('/{entreprise}', [EntrepriseController::class, 'update']);
        Route::post('/{entreprise}/profile-image', [EntrepriseController::class, 'updateProfileImage']);
        Route::delete('/{entreprise}', [EntrepriseController::class, 'destroy']);
    });
});
