// Simple test script to verify authentication pages
const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Authentication Pages...\n');

const authDir = './src/views/authentication';
const requiredFiles = [
  'Login.tsx',
  'Register.tsx',
  'ForgotPassword.tsx',
  'Error.tsx'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(authDir, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} exists`);
    
    // Check if file has content
    const content = fs.readFileSync(filePath, 'utf8');
    if (content.length > 100) {
      console.log(`   📄 File has content (${content.length} characters)`);
    } else {
      console.log(`   ⚠️  File seems empty or too small`);
    }
  } else {
    console.log(`❌ ${file} is missing`);
    allFilesExist = false;
  }
});

// Check router configuration
console.log('\n🔍 Checking Router Configuration...');
const routerPath = './src/routes/Router.tsx';
if (fs.existsSync(routerPath)) {
  const routerContent = fs.readFileSync(routerPath, 'utf8');
  
  const routes = [
    'login',
    'register', 
    'forgot-password',
    '404'
  ];
  
  routes.forEach(route => {
    if (routerContent.includes(`'${route}'`)) {
      console.log(`✅ Route /${route} is configured`);
    } else {
      console.log(`❌ Route /${route} is missing`);
    }
  });
  
  if (routerContent.includes('ForgotPassword')) {
    console.log(`✅ ForgotPassword component is imported`);
  } else {
    console.log(`❌ ForgotPassword component import is missing`);
  }
} else {
  console.log(`❌ Router.tsx is missing`);
  allFilesExist = false;
}

console.log('\n📊 Summary:');
if (allFilesExist) {
  console.log('🎉 All authentication pages are ready!');
  console.log('\n🚀 You can now run the application with: npm run dev');
  console.log('\n📱 Available routes:');
  console.log('   - http://localhost:3000/auth/login');
  console.log('   - http://localhost:3000/auth/register');
  console.log('   - http://localhost:3000/auth/forgot-password');
  console.log('   - http://localhost:3000/auth/404');
} else {
  console.log('❌ Some files are missing. Please check the errors above.');
}

console.log('\n🎨 Design Features:');
console.log('   - Modern purple gradient background');
console.log('   - Glass morphism effects');
console.log('   - Responsive two-column layout');
console.log('   - Floating decorative elements');
console.log('   - Consistent Material-UI styling');
