# Configuration du système d'authentification

Ce document explique comment configurer et utiliser le système d'authentification entre le front-end React et le back-end Laravel.

## Configuration du back-end (<PERSON><PERSON>)

1. Installer Laravel Sanctum pour l'authentification API:
   ```
   cd back-end
   composer require laravel/sanctum
   ```

2. Exécuter les migrations pour créer les tables nécessaires:
   ```
   cd back-end
   php artisan migrate
   ```

3. Démarrer le serveur Laravel:
   ```
   cd back-end
   php artisan serve
   ```

## Configuration du front-end (React)

1. Configurer l'URL de l'API dans le fichier `src/services/api.ts`:
   ```typescript
   // Définir l'URL de base de l'API
   const API_URL = 'http://localhost:8000/api';
   
   // Variable pour activer/désactiver le mode mock
   const USE_MOCK = false; // Mettre à false pour utiliser l'API réelle
   ```

2. Utiliser les services d'authentification dans vos composants:
   ```typescript
   import { useAuthContext } from '../contexts/AuthContext';
   
   const { register, login, logout, user } = useAuthContext();
   
   // Inscription
   const registerUser = async () => {
     const success = await register({
       name: 'John Doe',
       email: '<EMAIL>',
       password: 'password',
       password_confirmation: 'password',
       role: 'client'
     });
     
     if (success) {
       // Redirection ou autre action après inscription réussie
     }
   };
   
   // Connexion
   const loginUser = async () => {
     const success = await login('<EMAIL>', 'password');
     
     if (success) {
       // Redirection ou autre action après connexion réussie
     }
   };
   
   // Déconnexion
   const logoutUser = () => {
     logout();
     // Redirection ou autre action après déconnexion
   };
   ```

## Flux d'authentification

1. **Inscription**:
   - L'utilisateur remplit le formulaire d'inscription
   - Les données sont envoyées à l'API Laravel via la fonction `register`
   - Si l'inscription réussit, un token d'authentification est retourné et stocké dans le localStorage
   - L'utilisateur est automatiquement connecté et redirigé vers le dashboard approprié

2. **Connexion**:
   - L'utilisateur remplit le formulaire de connexion
   - Les données sont envoyées à l'API Laravel via la fonction `login`
   - Si la connexion réussit, un token d'authentification est retourné et stocké dans le localStorage
   - L'utilisateur est redirigé vers le dashboard approprié

3. **Déconnexion**:
   - L'utilisateur clique sur le bouton de déconnexion
   - La fonction `logout` est appelée, qui supprime le token du localStorage
   - L'utilisateur est redirigé vers la page d'accueil

## Gestion des profils

Après l'inscription ou la connexion, les profils spécifiques (client, coursier, entreprise) sont créés ou récupérés selon le rôle de l'utilisateur:

- **Client**: Profil avec informations personnelles et professionnelles
- **Coursier**: Profil avec informations personnelles et détails du véhicule
- **Entreprise**: Profil avec informations de l'entreprise et détails juridiques

Ces profils sont gérés via les services API correspondants:
- `apiService.client`
- `apiService.coursier`
- `apiService.entreprise`

## Tests

Pour tester l'API d'authentification, vous pouvez exécuter les tests PHPUnit:

```
cd back-end
php artisan test --filter=AuthTest
```
