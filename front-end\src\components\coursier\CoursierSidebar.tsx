import { IconType } from "react-icons";
import CoursierSection from "./CoursierSection";

interface SectionProps {
  icon: IconType;
  label: string;
  link: string;
}

interface CoursierSidebarProps {
  sections: SectionProps[];
  onLinkClick: (link: string) => void;
}

function CoursierSidebar({ sections, onLinkClick }: CoursierSidebarProps) {
  return (
    <div className="flex flex-col w-62 h-full pt-12 shadow-xl rounded-br-3xl bg-blue-100">
      {sections.map((section, index) => (
        <CoursierSection
          key={index}
          icon={<section.icon />}
          label={section.label}
          link={section.link}
          onLinkClick={onLinkClick}
        />
      ))}
    </div>
  );
}

export default CoursierSidebar;
