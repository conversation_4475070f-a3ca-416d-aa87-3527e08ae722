import CommandeDetails from "../../components/coursier/CommandeDetails";
import InnerPageContainer from "../../components/InnerPageContainer";
import useCommande from "../../hooks/coursier/useCommande";
import { useParams } from "react-router-dom";

const CommandePage = () => {
  const { id } = useParams();

  const { data: commande, isLoading, error } = useCommande(id!);


  return (
    <InnerPageContainer>
      {isLoading && <p>Loading...</p>}
      {error && <p>Error: {error.message || "An error occurred"}</p>}
      {commande && <CommandeDetails commande={commande} />}
    </InnerPageContainer>
  );
};

export default CommandePage;


