import { useNavigate } from "react-router-dom";
import { Commande } from "../../models/coursier/commande";


interface Props {
    commande: Commande;

}


const OrderItem = ({ commande }: Props) => {

  const navigate = useNavigate();

  return (
    <div className="card bg-base-100 shadow-xl mb-4">
      <div className="card-body p-4">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          <div>
            <h3 className="font-bold">Nom de client</h3>
            <p>{commande.nom_client}</p>
          </div>
          <div>
            <h3 className="font-bold">Ville</h3>
            <p>{commande.ville_client}</p>
          </div>
          <div>
            <h3 className="font-bold">Date</h3>
            <p>{commande.date_creation}</p>
          </div>
          <div>
            <h3 className="font-bold">Adress</h3>
            <p>{commande.adresse_client}</p>
          </div>
          <div>
            <h3 className="font-bold">Nom d'entreprise</h3>
            <p>{commande.nom_societe}</p>
          </div>
          <div>
            <h3 className="font-bold">Adress d'entreprise</h3>
            <p>{commande.adresse_societe}</p>
          </div>
        </div>
        <div className="card-actions justify-end mt-2">
          <button
            className="btn btn-primary btn-sm"
            onClick={() => navigate(`/coursier/commandes/${commande.id}`)}
          >
            Voir Plus
          </button>
        </div>
      </div>
    </div>
  );};


export default OrderItem;
