import React from 'react';
import { SignUpFormData } from '../VerticalSignUpForm';
import { FaUser, FaBuilding, FaMotorcycle } from 'react-icons/fa';

interface AccountTypeStepProps {
  formData: SignUpFormData;
  updateFormData: (data: Partial<SignUpFormData>) => void;
  onNext: () => void;
}

const AccountTypeStep: React.FC<AccountTypeStepProps> = ({ 
  formData, 
  updateFormData, 
  onNext 
}) => {
  const handleSelectAccountType = (type: 'personal' | 'enterprise' | 'courier') => {
    updateFormData({ accountType: type });
  };

  const handleContinue = () => {
    if (formData.accountType) {
      onNext();
    }
  };

  const accountTypes = [
    { 
      id: 'personal', 
      title: 'Personal Account', 
      description: 'For individual users looking to use our services',
      icon: <FaUser className="text-indigo-600 text-xl" />
    },
    { 
      id: 'enterprise', 
      title: 'Enterprise Account', 
      description: 'For businesses and organizations',
      icon: <FaBuilding className="text-indigo-600 text-xl" />
    },
    { 
      id: 'courier', 
      title: 'Courier Account', 
      description: 'For delivery partners',
      icon: <FaMotorcycle className="text-indigo-600 text-xl" />
    }
  ];

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">Select Account Type</h2>
      <p className="text-gray-600 mb-8">
        Choose the type of account that best suits your needs
      </p>
      
      <div className="space-y-4 mb-8">
        {accountTypes.map(type => (
          <div 
            key={type.id}
            className={`p-4 border rounded-lg cursor-pointer transition-all ${
              formData.accountType === type.id 
                ? 'border-indigo-600 bg-indigo-50' 
                : 'border-gray-200 hover:border-indigo-300'
            }`}
            onClick={() => handleSelectAccountType(type.id as 'personal' | 'enterprise' | 'courier')}
          >
            <div className="flex items-center">
              <div className="mr-4">
                {type.icon}
              </div>
              <div>
                <h3 className="font-medium">{type.title}</h3>
                <p className="text-sm text-gray-500">{type.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <button
        type="button"
        onClick={handleContinue}
        disabled={!formData.accountType}
        className={`w-full py-2 px-4 rounded-md text-white font-medium ${
          formData.accountType 
            ? 'bg-indigo-600 hover:bg-indigo-700' 
            : 'bg-gray-300 cursor-not-allowed'
        }`}
      >
        Continue
      </button>
    </div>
  );
};

export default AccountTypeStep;
