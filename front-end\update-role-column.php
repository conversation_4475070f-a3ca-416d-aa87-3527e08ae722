<?php
// Script pour modifier la colonne 'role' dans la table 'users'

// Paramètres de connexion à la base de données
$host = 'localhost';
$db = 'charikti';  // Remplacez par le nom de votre base de données
$user = 'root';    // Remplacez par votre nom d'utilisateur MySQL
$pass = '';        // Remplacez par votre mot de passe MySQL
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    // Connexion à la base de données
    $pdo = new PDO($dsn, $user, $pass, $options);
    
    // Vérifier le type actuel de la colonne 'role'
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'role'");
    $column = $stmt->fetch();
    
    echo "Structure actuelle de la colonne 'role': " . $column['Type'] . "\n";
    
    // Si c'est un ENUM, modifier pour inclure 'admin'
    if (strpos($column['Type'], 'enum') !== false) {
        // Extraire les valeurs actuelles de l'enum
        preg_match("/^enum\((.*)\)$/", $column['Type'], $matches);
        $values = str_getcsv($matches[1], ',', "'");
        
        echo "Valeurs actuelles: " . implode(', ', $values) . "\n";
        
        // Vérifier si 'admin' est déjà présent
        if (!in_array('admin', $values)) {
            // Ajouter 'admin' aux valeurs
            $values[] = 'admin';
            
            // Construire la nouvelle définition ENUM
            $newEnum = "enum('" . implode("','", $values) . "')";
            
            // Modifier la colonne
            $pdo->exec("ALTER TABLE users MODIFY COLUMN role $newEnum");
            
            echo "Colonne 'role' modifiée avec succès. Nouvelles valeurs: " . implode(', ', $values) . "\n";
        } else {
            echo "'admin' est déjà présent dans les valeurs de l'enum.\n";
        }
    } else {
        // Si ce n'est pas un ENUM, modifier en VARCHAR pour plus de flexibilité
        $pdo->exec("ALTER TABLE users MODIFY COLUMN role VARCHAR(20) NOT NULL");
        echo "Colonne 'role' modifiée en VARCHAR(20).\n";
    }
    
    // Vérifier la nouvelle structure
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'role'");
    $column = $stmt->fetch();
    echo "Nouvelle structure de la colonne 'role': " . $column['Type'] . "\n";
    
} catch (\PDOException $e) {
    echo "Erreur de base de données: " . $e->getMessage() . "\n";
    exit(1);
}
