import React, { useState } from 'react';
import {
  Box,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Stack,
  TextField,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Card,
  CardContent,
  Grid,
  useTheme,
  alpha,
  InputAdornment,
  IconButton,
} from '@mui/material';
import {
  IconUser,
  IconMail,
  IconLock,
  IconEye,
  IconEyeOff,
  IconPlus,
  IconCircle,
  IconBriefcase,
} from '@tabler/icons-react';
import { Link, useNavigate } from 'react-router';
import { useAuthContext } from 'src/context/AuthContext';

const Register = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const { register, loading, error } = useAuthContext();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: 'client' as 'client' | 'coursier' | 'entreprise',
  });

  const [formErrors, setFormErrors] = useState({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: '',
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear error when user starts typing
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleRoleChange = (e: SelectChangeEvent) => {
    setFormData(prev => ({
      ...prev,
      role: e.target.value as 'client' | 'coursier' | 'entreprise',
    }));
    
    if (formErrors.role) {
      setFormErrors(prev => ({
        ...prev,
        role: '',
      }));
    }
  };

  const validateForm = () => {
    const errors = {
      name: '',
      email: '',
      password: '',
      password_confirmation: '',
      role: '',
    };

    if (!formData.name) {
      errors.name = 'Le nom est requis';
    } else if (formData.name.length < 2) {
      errors.name = 'Le nom doit contenir au moins 2 caractères';
    }

    if (!formData.email) {
      errors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Format d\'email invalide';
    }

    if (!formData.password) {
      errors.password = 'Le mot de passe est requis';
    } else if (formData.password.length < 8) {
      errors.password = 'Le mot de passe doit contenir au moins 8 caractères';
    }

    if (!formData.password_confirmation) {
      errors.password_confirmation = 'La confirmation du mot de passe est requise';
    } else if (formData.password !== formData.password_confirmation) {
      errors.password_confirmation = 'Les mots de passe ne correspondent pas';
    }

    if (!formData.role) {
      errors.role = 'Le type de compte est requis';
    }

    setFormErrors(errors);
    return Object.values(errors).every(error => !error);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const success = await register(formData);
    
    if (success) {
      navigate('/dashboard');
    }
  };

  const roleOptions = [
    { value: 'client', label: 'Client', description: 'Pour passer des commandes' },
    { value: 'coursier', label: 'Coursier', description: 'Pour effectuer des livraisons' },
    { value: 'entreprise', label: 'Entreprise', description: 'Pour les services professionnels' },
  ];

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `
            radial-gradient(circle at 20% 80%, ${alpha('#ffffff', 0.1)} 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, ${alpha('#ffffff', 0.1)} 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, ${alpha('#ffffff', 0.05)} 0%, transparent 50%)
          `,
        },
      }}
    >
      {/* Decorative elements */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          left: '10%',
          width: '100px',
          height: '100px',
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        <IconPlus size={24} color="white" />
      </Box>
      <Box
        sx={{
          position: 'absolute',
          top: '20%',
          right: '15%',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        {[...Array(9)].map((_, i) => (
          <Box key={i} sx={{ width: '4px', height: '4px', bgcolor: 'white', borderRadius: '50%' }} />
        ))}
      </Box>
      <Box
        sx={{
          position: 'absolute',
          bottom: '30%',
          left: '5%',
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        <IconCircle size={20} color="white" />
      </Box>
      <Box
        sx={{
          position: 'absolute',
          bottom: '10%',
          right: '20%',
          width: '50px',
          height: '50px',
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        <IconPlus size={16} color="white" />
      </Box>

      <Grid container sx={{ height: '100vh', zIndex: 2 }}>
        {/* Left side - Welcome section */}
        <Grid
          item
          xs={12}
          md={7}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'flex-start',
            px: { xs: 4, md: 8 },
            py: 4,
            position: 'relative',
          }}
        >
          {/* Decorative flowing lines */}
          <Box
            sx={{
              position: 'absolute',
              top: '10%',
              left: '5%',
              width: '200px',
              height: '200px',
              opacity: 0.1,
              '&::before': {
                content: '""',
                position: 'absolute',
                width: '100%',
                height: '100%',
                border: '2px solid white',
                borderRadius: '50% 30% 70% 40%',
                animation: 'float 6s ease-in-out infinite',
              },
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              bottom: '20%',
              right: '10%',
              width: '150px',
              height: '150px',
              opacity: 0.1,
              '&::before': {
                content: '""',
                position: 'absolute',
                width: '100%',
                height: '100%',
                border: '2px solid white',
                borderRadius: '40% 60% 30% 70%',
                animation: 'float 8s ease-in-out infinite reverse',
              },
            }}
          />

          <Typography
            variant="h2"
            sx={{
              color: 'white',
              fontWeight: 700,
              fontSize: { xs: '2.5rem', md: '3.5rem' },
              mb: 2,
              lineHeight: 1.2,
            }}
          >
            Join us today!
          </Typography>
          <Typography
            variant="h6"
            sx={{
              color: alpha('#ffffff', 0.9),
              fontSize: '1.1rem',
              maxWidth: '400px',
              lineHeight: 1.6,
            }}
          >
            Create your account and start your journey with us.
          </Typography>
        </Grid>

        {/* Right side - Register form */}
        <Grid
          item
          xs={12}
          md={5}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            px: { xs: 2, md: 4 },
            py: 4,
          }}
        >
          <Card
            elevation={0}
            sx={{
              width: '100%',
              maxWidth: '400px',
              bgcolor: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              borderRadius: 4,
              border: `1px solid ${alpha('#ffffff', 0.2)}`,
              maxHeight: '90vh',
              overflow: 'auto',
            }}
          >
            <CardContent sx={{ p: 4 }}>
              <Typography
                variant="h4"
                sx={{
                  color: '#2d3748',
                  fontWeight: 600,
                  mb: 4,
                  textAlign: 'center',
                }}
              >
                Create Account
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              <Box component="form" onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <TextField
                    fullWidth
                    placeholder="Full name"
                    name="name"
                    type="text"
                    value={formData.name}
                    onChange={handleChange}
                    error={!!formErrors.name}
                    helperText={formErrors.name}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <IconUser size={20} color="#9ca3af" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        bgcolor: '#f8fafc',
                        border: 'none',
                        '& fieldset': {
                          border: 'none',
                        },
                        '&:hover fieldset': {
                          border: 'none',
                        },
                        '&.Mui-focused fieldset': {
                          border: `2px solid ${theme.palette.primary.main}`,
                        },
                      },
                    }}
                  />

                  <TextField
                    fullWidth
                    placeholder="Email address"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    error={!!formErrors.email}
                    helperText={formErrors.email}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <IconMail size={20} color="#9ca3af" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        bgcolor: '#f8fafc',
                        border: 'none',
                        '& fieldset': {
                          border: 'none',
                        },
                        '&:hover fieldset': {
                          border: 'none',
                        },
                        '&.Mui-focused fieldset': {
                          border: `2px solid ${theme.palette.primary.main}`,
                        },
                      },
                    }}
                  />

                  <FormControl fullWidth error={!!formErrors.role}>
                    <Select
                      name="role"
                      value={formData.role}
                      onChange={handleRoleChange}
                      displayEmpty
                      startAdornment={
                        <InputAdornment position="start">
                          <IconBriefcase size={20} color="#9ca3af" />
                        </InputAdornment>
                      }
                      sx={{
                        borderRadius: 3,
                        bgcolor: '#f8fafc',
                        border: 'none',
                        '& fieldset': {
                          border: 'none',
                        },
                        '&:hover fieldset': {
                          border: 'none',
                        },
                        '&.Mui-focused fieldset': {
                          border: `2px solid ${theme.palette.primary.main}`,
                        },
                      }}
                    >
                      {roleOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          <Box>
                            <Typography variant="body1">{option.label}</Typography>
                            <Typography variant="caption" color="textSecondary">
                              {option.description}
                            </Typography>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                    {formErrors.role && (
                      <Typography variant="caption" color="error" sx={{ mt: 1, ml: 1 }}>
                        {formErrors.role}
                      </Typography>
                    )}
                  </FormControl>

                  <TextField
                    fullWidth
                    placeholder="Password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleChange}
                    error={!!formErrors.password}
                    helperText={formErrors.password}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <IconLock size={20} color="#9ca3af" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                            size="small"
                          >
                            {showPassword ? (
                              <IconEyeOff size={20} color="#9ca3af" />
                            ) : (
                              <IconEye size={20} color="#9ca3af" />
                            )}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        bgcolor: '#f8fafc',
                        border: 'none',
                        '& fieldset': {
                          border: 'none',
                        },
                        '&:hover fieldset': {
                          border: 'none',
                        },
                        '&.Mui-focused fieldset': {
                          border: `2px solid ${theme.palette.primary.main}`,
                        },
                      },
                    }}
                  />

                  <TextField
                    fullWidth
                    placeholder="Confirm password"
                    name="password_confirmation"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.password_confirmation}
                    onChange={handleChange}
                    error={!!formErrors.password_confirmation}
                    helperText={formErrors.password_confirmation}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <IconLock size={20} color="#9ca3af" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            edge="end"
                            size="small"
                          >
                            {showConfirmPassword ? (
                              <IconEyeOff size={20} color="#9ca3af" />
                            ) : (
                              <IconEye size={20} color="#9ca3af" />
                            )}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        bgcolor: '#f8fafc',
                        border: 'none',
                        '& fieldset': {
                          border: 'none',
                        },
                        '&:hover fieldset': {
                          border: 'none',
                        },
                        '&.Mui-focused fieldset': {
                          border: `2px solid ${theme.palette.primary.main}`,
                        },
                      },
                    }}
                  />

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    disabled={loading}
                    sx={{
                      mt: 3,
                      py: 1.5,
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      textTransform: 'none',
                      fontSize: '1rem',
                      fontWeight: 600,
                      boxShadow: '0 4px 15px rgba(102, 126, 234, 0.4)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                        boxShadow: '0 6px 20px rgba(102, 126, 234, 0.6)',
                      },
                      '&:disabled': {
                        background: '#e5e7eb',
                        color: '#9ca3af',
                        boxShadow: 'none',
                      },
                    }}
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Create Account'}
                  </Button>

                  <Typography
                    variant="body2"
                    sx={{
                      textAlign: 'center',
                      color: '#6b7280',
                      mt: 2,
                    }}
                  >
                    Already have an account?{' '}
                    <Typography
                      component={Link}
                      to="/auth/login"
                      sx={{
                        color: theme.palette.primary.main,
                        textDecoration: 'none',
                        fontWeight: 600,
                        '&:hover': {
                          textDecoration: 'underline',
                        },
                      }}
                    >
                      Sign In
                    </Typography>
                  </Typography>
                </Stack>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Global styles for animations */}
      <style>
        {`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(10deg); }
          }
        `}
      </style>
    </Box>
  );
};

export default Register;
