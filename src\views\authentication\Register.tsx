import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Stack,
  TextField,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import { Link, useNavigate } from 'react-router';
import { useAuthContext } from 'src/context/AuthContext';

const Register = () => {
  const navigate = useNavigate();
  const { register, loading, error } = useAuthContext();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: 'client' as 'client' | 'coursier' | 'entreprise',
  });
  
  const [formErrors, setFormErrors] = useState({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear error when user starts typing
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleRoleChange = (e: SelectChangeEvent) => {
    setFormData(prev => ({
      ...prev,
      role: e.target.value as 'client' | 'coursier' | 'entreprise',
    }));
    
    if (formErrors.role) {
      setFormErrors(prev => ({
        ...prev,
        role: '',
      }));
    }
  };

  const validateForm = () => {
    const errors = {
      name: '',
      email: '',
      password: '',
      password_confirmation: '',
      role: '',
    };

    if (!formData.name) {
      errors.name = 'Le nom est requis';
    } else if (formData.name.length < 2) {
      errors.name = 'Le nom doit contenir au moins 2 caractères';
    }

    if (!formData.email) {
      errors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Format d\'email invalide';
    }

    if (!formData.password) {
      errors.password = 'Le mot de passe est requis';
    } else if (formData.password.length < 8) {
      errors.password = 'Le mot de passe doit contenir au moins 8 caractères';
    }

    if (!formData.password_confirmation) {
      errors.password_confirmation = 'La confirmation du mot de passe est requise';
    } else if (formData.password !== formData.password_confirmation) {
      errors.password_confirmation = 'Les mots de passe ne correspondent pas';
    }

    if (!formData.role) {
      errors.role = 'Le type de compte est requis';
    }

    setFormErrors(errors);
    return Object.values(errors).every(error => !error);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const success = await register(formData);
    
    if (success) {
      navigate('/dashboard');
    }
  };

  const roleOptions = [
    { value: 'client', label: 'Client', description: 'Pour passer des commandes' },
    { value: 'coursier', label: 'Coursier', description: 'Pour effectuer des livraisons' },
    { value: 'entreprise', label: 'Entreprise', description: 'Pour les services professionnels' },
  ];

  return (
    <form onSubmit={handleSubmit}>
      <Stack spacing={3}>
        <Box>
          <Typography variant="h3" fontWeight="700" mb={1}>
            Inscription
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Créez votre compte Charikti
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box>
          <Typography
            variant="subtitle1"
            fontWeight={600}
            component="label"
            htmlFor="name"
            mb="5px"
          >
            Nom complet
          </Typography>
          <TextField
            id="name"
            name="name"
            type="text"
            variant="outlined"
            fullWidth
            value={formData.name}
            onChange={handleChange}
            error={!!formErrors.name}
            helperText={formErrors.name}
            placeholder="Entrez votre nom complet"
          />
        </Box>

        <Box>
          <Typography
            variant="subtitle1"
            fontWeight={600}
            component="label"
            htmlFor="email"
            mb="5px"
          >
            Email
          </Typography>
          <TextField
            id="email"
            name="email"
            type="email"
            variant="outlined"
            fullWidth
            value={formData.email}
            onChange={handleChange}
            error={!!formErrors.email}
            helperText={formErrors.email}
            placeholder="Entrez votre email"
          />
        </Box>

        <Box>
          <Typography
            variant="subtitle1"
            fontWeight={600}
            component="label"
            htmlFor="role"
            mb="5px"
          >
            Type de compte
          </Typography>
          <FormControl fullWidth error={!!formErrors.role}>
            <Select
              id="role"
              name="role"
              value={formData.role}
              onChange={handleRoleChange}
              displayEmpty
            >
              {roleOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Box>
                    <Typography variant="body1">{option.label}</Typography>
                    <Typography variant="caption" color="textSecondary">
                      {option.description}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
            {formErrors.role && (
              <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                {formErrors.role}
              </Typography>
            )}
          </FormControl>
        </Box>

        <Box>
          <Typography
            variant="subtitle1"
            fontWeight={600}
            component="label"
            htmlFor="password"
            mb="5px"
          >
            Mot de passe
          </Typography>
          <TextField
            id="password"
            name="password"
            type="password"
            variant="outlined"
            fullWidth
            value={formData.password}
            onChange={handleChange}
            error={!!formErrors.password}
            helperText={formErrors.password}
            placeholder="Entrez votre mot de passe"
          />
        </Box>

        <Box>
          <Typography
            variant="subtitle1"
            fontWeight={600}
            component="label"
            htmlFor="password_confirmation"
            mb="5px"
          >
            Confirmer le mot de passe
          </Typography>
          <TextField
            id="password_confirmation"
            name="password_confirmation"
            type="password"
            variant="outlined"
            fullWidth
            value={formData.password_confirmation}
            onChange={handleChange}
            error={!!formErrors.password_confirmation}
            helperText={formErrors.password_confirmation}
            placeholder="Confirmez votre mot de passe"
          />
        </Box>
      </Stack>

      <Box mt={3}>
        <Button
          color="primary"
          variant="contained"
          size="large"
          fullWidth
          type="submit"
          disabled={loading}
          sx={{ mb: 2 }}
        >
          {loading ? (
            <CircularProgress size={24} color="inherit" />
          ) : (
            'Créer un compte'
          )}
        </Button>
        
        <Typography
          variant="body2"
          textAlign="center"
          color="textSecondary"
        >
          Déjà un compte ?{' '}
          <Typography
            component={Link}
            to="/auth/login"
            fontWeight="500"
            sx={{
              textDecoration: 'none',
              color: 'primary.main',
            }}
          >
            Se connecter
          </Typography>
        </Typography>
      </Box>
    </form>
  );
};

export default Register;
