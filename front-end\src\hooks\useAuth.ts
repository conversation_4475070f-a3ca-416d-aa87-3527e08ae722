import { useState, useEffect } from 'react';
import api from '../services/api';
import apiService, { RegisterData } from '../services/apiService';

// Fonction utilitaire pour extraire les messages d'erreur
const getErrorMessage = (error: any, defaultMessage: string): string => {
  if (error.response && error.response.data && error.response.data.message) {
    return error.response.data.message;
  } else if (error.message) {
    return error.message;
  }
  return defaultMessage;
};

export interface User {
  id: number;
  name: string;
  email: string;
  role: 'client' | 'coursier' | 'entreprise' | 'admin';
  profileImage?: string;
}

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fonction pour récupérer les informations de l'utilisateur connecté
  const fetchUser = async () => {
    try {
      setLoading(true);
      const response = await api.get<User>('/user');

      // Récupérer l'image de profil depuis le localStorage si disponible
      if (response.data && response.data.role) {
        const storageKey = `${response.data.role}_profile_image`;
        const storedImage = localStorage.getItem(storageKey);

        if (storedImage && storedImage.startsWith('data:image')) {
          console.log(`Image récupérée depuis localStorage[${storageKey}] lors du fetchUser`);
          response.data.profileImage = storedImage;
        } else if (!response.data.profileImage) {
          // Si aucune image n'est trouvée, utiliser une image par défaut
          switch (response.data.role) {
            case 'client':
              response.data.profileImage = 'https://randomuser.me/api/portraits/men/1.jpg';
              break;
            case 'coursier':
              response.data.profileImage = 'https://randomuser.me/api/portraits/men/2.jpg';
              break;
            case 'entreprise':
              response.data.profileImage = 'https://randomuser.me/api/portraits/lego/1.jpg';
              break;
            case 'admin':
              response.data.profileImage = 'https://randomuser.me/api/portraits/men/10.jpg';
              break;
          }
        }
      }

      setUser(response.data);

      // Stocker le rôle de l'utilisateur dans le localStorage pour la redirection
      if (response.data && response.data.role) {
        localStorage.setItem('user_role', response.data.role);
        console.log('User role stored in localStorage from fetchUser:', response.data.role);
      }

      setError(null);
      return response.data;
    } catch (err) {
      const errorMsg = getErrorMessage(err, 'Erreur lors de la récupération des informations utilisateur');
      console.error('Fetch user error:', err);
      setError(errorMsg);
      setUser(null);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Fonction de connexion
  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.post<{ token: string; user: User }>('/login', { email, password });

      // Stocker le token dans le localStorage
      localStorage.setItem('token', response.data.token);

      // Si l'API renvoie directement les informations utilisateur, les utiliser
      if (response.data.user) {
        setUser(response.data.user);

        // Stocker le rôle de l'utilisateur dans le localStorage pour la redirection
        if (response.data.user.role) {
          localStorage.setItem('user_role', response.data.user.role);
          console.log('User role stored in localStorage:', response.data.user.role);
        }

        return true;
      }

      // Sinon, récupérer les informations utilisateur
      const userData = await fetchUser();
      return !!userData;
    } catch (err) {
      const errorMsg = getErrorMessage(err, 'Identifiants incorrects');
      console.error('Login error:', err);
      setError(errorMsg);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Fonction d'inscription
  const register = async (registerData: RegisterData) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Registering user with data:', registerData);

      // Utiliser directement fetch au lieu de apiService
      const response = await fetch('http://localhost:8000/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(registerData),
        credentials: 'include',
        mode: 'cors'
      });

      const data = await response.json();
      console.log('Registration response:', response.status, data);

      if (!response.ok) {
        if (data.errors) {
          const validationErrors = Object.values(data.errors).flat().join(', ');
          throw new Error(validationErrors);
        } else if (data.message) {
          throw new Error(data.message);
        } else {
          throw new Error('Erreur lors de l\'inscription: ' + response.status);
        }
      }

      // Stocker le token dans le localStorage
      localStorage.setItem('token', data.token);

      // Définir l'utilisateur
      setUser(data.user);
      return true;
    } catch (err: any) {
      console.error('Register error:', err);
      setError(err.message || 'Erreur lors de l\'inscription');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Fonction de déconnexion
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user_role');
    setUser(null);
  };

  // Vérifier si l'utilisateur est connecté au chargement du composant
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      fetchUser();
    } else {
      setLoading(false);
    }
  }, []);

  // Mettre à jour l'image de profil de l'utilisateur
  const updateProfileImage = (imageUrl: string) => {
    if (user) {
      try {
        // Stocker l'image dans le localStorage
        if (user.role) {
          const storageKey = `${user.role}_profile_image`;
          console.log(`Stockage de l'image dans localStorage[${storageKey}] depuis updateProfileImage`);
          localStorage.setItem(storageKey, imageUrl);
        }

        // Mettre à jour l'état local
        setUser({
          ...user,
          profileImage: imageUrl
        });

        // Vous pourriez également envoyer cette mise à jour au serveur si nécessaire
        // await api.put('/user/profile-image', { profileImage: imageUrl });

        return true;
      } catch (err) {
        console.error('Error updating profile image:', err);
        return false;
      }
    }
    return false;
  };

  // Vérifier si l'utilisateur est administrateur
  const isAdmin = () => {
    return user?.role === 'admin';
  };

  return {
    user,
    loading,
    error,
    login,
    logout,
    register,
    fetchUser,
    updateProfileImage,
    isAdmin
  };
};

export default useAuth;
