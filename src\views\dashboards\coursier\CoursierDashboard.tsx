import React from 'react';
import {
  <PERSON>rid,
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  useTheme,
  alpha,
  Chip,
} from '@mui/material';
import {
  IconTruck,
  IconClock,
  IconCheck,
  IconCurrencyDollar,
  IconMapPin,
  IconPhone,
} from '@tabler/icons-react';
import PageContainer from 'src/components/container/PageContainer';
import DashboardCard from 'src/components/shared/DashboardCard';
import { useAuthContext } from 'src/context/AuthContext';

const CoursierDashboard = () => {
  const theme = useTheme();
  const { user } = useAuthContext();

  // Données simulées - à remplacer par de vraies données API
  const stats = [
    {
      title: 'Livraisons aujourd\'hui',
      value: '8',
      icon: IconTruck,
      color: theme.palette.primary.main,
      bgColor: alpha(theme.palette.primary.main, 0.1),
    },
    {
      title: 'En cours',
      value: '2',
      icon: Icon<PERSON><PERSON>,
      color: theme.palette.warning.main,
      bgColor: alpha(theme.palette.warning.main, 0.1),
    },
    {
      title: 'Termin<PERSON>',
      value: '6',
      icon: IconCheck,
      color: theme.palette.success.main,
      bgColor: alpha(theme.palette.success.main, 0.1),
    },
    {
      title: 'Gains du jour',
      value: '450 DH',
      icon: IconCurrencyDollar,
      color: theme.palette.secondary.main,
      bgColor: alpha(theme.palette.secondary.main, 0.1),
    },
  ];

  const availableOrders = [
    {
      id: 'CMD001',
      pickup: 'Casablanca Centre',
      destination: 'Ain Diab',
      distance: '12 km',
      payment: '35 DH',
      priority: 'Urgent',
      client: 'Ahmed Benali',
      phone: '+212 6 12 34 56 78',
    },
    {
      id: 'CMD002',
      pickup: 'Maarif',
      destination: 'Sidi Bernoussi',
      distance: '8 km',
      payment: '25 DH',
      priority: 'Normal',
      client: 'Fatima Zahra',
      phone: '+212 6 87 65 43 21',
    },
    {
      id: 'CMD003',
      pickup: 'Hay Hassani',
      destination: 'Derb Ghallef',
      distance: '15 km',
      payment: '40 DH',
      priority: 'Express',
      client: 'Omar Idrissi',
      phone: '+212 6 11 22 33 44',
    },
  ];

  const currentDeliveries = [
    {
      id: 'CMD004',
      destination: 'Rabat Agdal',
      client: 'Laila Bennani',
      phone: '+212 6 55 44 33 22',
      estimatedTime: '25 min',
      status: 'En route',
    },
    {
      id: 'CMD005',
      destination: 'Salé Médina',
      client: 'Youssef Alami',
      phone: '+212 6 99 88 77 66',
      estimatedTime: '45 min',
      status: 'Récupéré',
    },
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Urgent':
        return theme.palette.error.main;
      case 'Express':
        return theme.palette.warning.main;
      default:
        return theme.palette.info.main;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'En route':
        return theme.palette.success.main;
      case 'Récupéré':
        return theme.palette.warning.main;
      default:
        return theme.palette.info.main;
    }
  };

  return (
    <PageContainer title="Dashboard Coursier" description="Tableau de bord coursier">
      <Box>
        {/* Welcome Section */}
        <DashboardCard>
          <Box sx={{ mb: 4 }}>
            <Typography variant="h4" gutterBottom>
              Bonjour, {user?.name} !
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Gérez vos livraisons et maximisez vos gains aujourd'hui.
            </Typography>
          </Box>
        </DashboardCard>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                sx={{
                  height: '100%',
                  background: `linear-gradient(135deg, ${stat.bgColor} 0%, ${alpha(stat.color, 0.05)} 100%)`,
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box
                      sx={{
                        p: 1,
                        borderRadius: 2,
                        bgcolor: stat.color,
                        color: 'white',
                        mr: 2,
                      }}
                    >
                      <stat.icon size={24} />
                    </Box>
                    <Typography variant="h4" fontWeight="bold">
                      {stat.value}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="textSecondary">
                    {stat.title}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={3}>
          {/* Commandes disponibles */}
          <Grid item xs={12} md={8}>
            <DashboardCard title="Commandes disponibles">
              <Box>
                {availableOrders.map((order, index) => (
                  <Card
                    key={index}
                    sx={{
                      mb: 2,
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: theme.shadows[4],
                      },
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Box>
                          <Typography variant="h6" fontWeight="600">
                            {order.id}
                          </Typography>
                          <Chip
                            label={order.priority}
                            size="small"
                            sx={{
                              bgcolor: alpha(getPriorityColor(order.priority), 0.1),
                              color: getPriorityColor(order.priority),
                              fontWeight: 600,
                            }}
                          />
                        </Box>
                        <Typography variant="h6" color="primary" fontWeight="bold">
                          {order.payment}
                        </Typography>
                      </Box>
                      
                      <Grid container spacing={2} sx={{ mb: 2 }}>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <IconMapPin size={16} color={theme.palette.text.secondary} />
                            <Typography variant="body2" sx={{ ml: 1 }}>
                              <strong>Récupération:</strong> {order.pickup}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <IconMapPin size={16} color={theme.palette.text.secondary} />
                            <Typography variant="body2" sx={{ ml: 1 }}>
                              <strong>Livraison:</strong> {order.destination}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="textSecondary">
                            <strong>Distance:</strong> {order.distance}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            <strong>Client:</strong> {order.client}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <IconPhone size={16} color={theme.palette.text.secondary} />
                            <Typography variant="body2" sx={{ ml: 1 }}>
                              {order.phone}
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                      
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button variant="contained" size="small" fullWidth>
                          Accepter
                        </Button>
                        <Button variant="outlined" size="small" fullWidth>
                          Détails
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            </DashboardCard>
          </Grid>

          {/* Livraisons en cours */}
          <Grid item xs={12} md={4}>
            <DashboardCard title="Livraisons en cours">
              <Box>
                {currentDeliveries.map((delivery, index) => (
                  <Card
                    key={index}
                    sx={{
                      mb: 2,
                      border: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Typography variant="subtitle1" fontWeight="600">
                          {delivery.id}
                        </Typography>
                        <Chip
                          label={delivery.status}
                          size="small"
                          sx={{
                            bgcolor: alpha(getStatusColor(delivery.status), 0.1),
                            color: getStatusColor(delivery.status),
                            fontWeight: 600,
                          }}
                        />
                      </Box>
                      
                      <Box sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <IconMapPin size={16} color={theme.palette.text.secondary} />
                          <Typography variant="body2" sx={{ ml: 1 }}>
                            {delivery.destination}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="textSecondary">
                          <strong>Client:</strong> {delivery.client}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <IconPhone size={16} color={theme.palette.text.secondary} />
                          <Typography variant="body2" sx={{ ml: 1 }}>
                            {delivery.phone}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <IconClock size={16} color={theme.palette.text.secondary} />
                          <Typography variant="body2" sx={{ ml: 1 }}>
                            <strong>ETA:</strong> {delivery.estimatedTime}
                          </Typography>
                        </Box>
                      </Box>
                      
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button variant="contained" size="small" fullWidth>
                          Livré
                        </Button>
                        <Button variant="outlined" size="small" fullWidth>
                          Appeler
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            </DashboardCard>
          </Grid>
        </Grid>
      </Box>
    </PageContainer>
  );
};

export default CoursierDashboard;
