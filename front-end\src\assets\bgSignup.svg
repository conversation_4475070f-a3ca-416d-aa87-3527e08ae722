<svg width="1440" height="1024" viewBox="0 0 1440 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1_7302)">
<rect width="1440" height="1024" fill="#94B9FF"/>
<g filter="url(#filter0_f_1_7302)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M364.716 -20.962C401.486 -16.1813 439.82 -33.8691 471.539 -14.6829C511.092 9.24261 557.279 46.1967 551.509 92.0268C545.762 137.673 478.102 140.362 447.67 174.893C412.508 214.793 417.917 302.835 364.716 303.988C311.87 305.134 308.525 221.736 278.08 178.571C256.718 148.282 223.581 128.125 215.22 92.0268C204.348 45.0813 187.459 -17.5427 225.926 -46.6161C264.93 -76.0955 316.215 -27.2678 364.716 -20.962Z" fill="#82B9E5" fill-opacity="0.6"/>
</g>
<g filter="url(#filter1_f_1_7302)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M49.3889 -108.457C85.7041 -121.499 125.063 -95.7269 158.659 -76.7377C192.466 -57.6297 227.588 -34.8242 242.87 0.902603C257.787 35.777 249.419 76.0611 239.705 112.731C231.432 143.956 205.285 165.657 191.485 194.859C176.114 227.385 184.293 274.753 153.992 294.106C123.989 313.268 84.9675 284.773 49.3889 285.613C12.6612 286.481 -27.9358 320.105 -58.1042 299.117C-89.1409 277.525 -69.1989 224.901 -86.346 191.183C-102.163 160.08 -155.769 147.512 -152.888 112.731C-149.909 76.7626 -100.046 66.3412 -73.9423 41.4477C-55.7677 24.116 -38.3427 7.67572 -22.3848 -11.7205C3.5659 -43.2627 10.9674 -94.6589 49.3889 -108.457Z" fill="#197FCE" fill-opacity="0.6"/>
</g>
<g filter="url(#filter2_f_1_7302)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M435.234 734.856C465.148 729.015 490.978 754.574 519.266 765.933C555.177 780.353 611.51 773.889 623.874 810.606C636.76 848.872 587.585 879.987 569.891 916.274C558.438 939.765 551.773 963.916 538.225 986.26C522.016 1012.99 510.585 1045.08 482.957 1059.66C453.507 1075.2 418.319 1071.76 385.383 1066.92C348.366 1061.48 308.424 1056.35 281.748 1030.08C254.417 1003.16 231.839 962.083 241.121 924.836C250.65 886.593 299.498 875.446 327.246 847.494C343.441 831.18 354.008 811.688 369.916 795.094C390.839 773.271 405.583 740.646 435.234 734.856Z" fill="#184970" fill-opacity="0.6"/>
</g>
<g filter="url(#filter3_f_1_7302)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M121.24 655.244C156.186 662.959 166.204 708.129 190.585 734.321C207.78 752.793 225.765 768.353 243.364 786.439C271.128 814.972 321.416 831.599 323.903 871.328C326.256 908.925 285.417 936.372 254.058 957.255C227.12 975.194 190.698 965.378 161.55 979.445C123.477 997.819 102.179 1049.39 59.931 1050.94C17.873 1052.49 -23.5835 1022.68 -45.9961 987.071C-67.7161 952.558 -61.0602 907.984 -55.4553 867.596C-50.942 835.074 -31.7993 808.398 -17.1243 779.025C-3.03531 750.824 5.45979 719.658 28.9564 698.638C54.8125 675.507 87.3594 647.764 121.24 655.244Z" fill="#CAE8FF" fill-opacity="0.7"/>
</g>
<g filter="url(#filter4_f_1_7302)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M327.082 304.886C380.618 310.78 441.352 276.911 485.364 307.876C529.465 338.904 518.515 407.156 541.597 455.812C565.131 505.421 623.842 540.955 621.956 595.804C620.061 650.874 576.107 697.368 532.139 730.735C492.42 760.877 437.254 752.033 391.106 770.992C337.161 793.154 296.941 861.236 239.518 850.949C183.249 840.87 159.381 772.849 129.639 724.154C104.07 682.291 95.6405 635.331 79.5378 589.017C59.0119 529.982 9.49084 475.785 21.9965 414.559C34.8716 351.523 83.9451 292.171 144.624 270.358C204.065 248.99 264.28 297.97 327.082 304.886Z" fill="#5461AF" fill-opacity="0.6"/>
</g>
</g>
<defs>
<filter id="filter0_f_1_7302" x="102" y="-156" width="550" height="560" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_1_7302"/>
</filter>
<filter id="filter1_f_1_7302" x="-253" y="-212" width="604" height="618" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_1_7302"/>
</filter>
<filter id="filter2_f_1_7302" x="139" y="634" width="587" height="537" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_1_7302"/>
</filter>
<filter id="filter3_f_1_7302" x="-161" y="554" width="585" height="597" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_1_7302"/>
</filter>
<filter id="filter4_f_1_7302" x="-80" y="165" width="802" height="787" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_1_7302"/>
</filter>
<clipPath id="clip0_1_7302">
<rect width="1440" height="1024" fill="white"/>
</clipPath>
</defs>
</svg>
