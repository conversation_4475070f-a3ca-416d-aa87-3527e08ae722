// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router';
import {
  Box,
  Menu,
  Avatar,
  Typography,
  Divider,
  Button,
  IconButton,
  Stack,
  Chip
} from '@mui/material';
import * as dropdownData from './data';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { IconMail, IconUser, IconSettings, IconLogout } from '@tabler/icons-react';

import ProfileImg from 'src/assets/images/profile/user-1.jpg';
import unlimitedImg from 'src/assets/images/backgrounds/unlimited-bg.png';
import { useAuthContext } from 'src/context/AuthContext';

const Profile = () => {
  const [anchorEl2, setAnchorEl2] = useState(null);
  const { user, logout, isAuthenticated } = useAuthContext();
  const navigate = useNavigate();

  const handleClick2 = (event: any) => {
    setAnchorEl2(event.currentTarget);
  };

  const handleClose2 = () => {
    setAnchorEl2(null);
  };

  const handleLogout = async () => {
    await logout();
    handleClose2();
    navigate('/');
  };

  const getRoleColor = (role?: string) => {
    switch (role) {
      case 'client':
        return 'success';
      case 'coursier':
        return 'warning';
      case 'entreprise':
        return 'info';
      case 'admin':
        return 'error';
      default:
        return 'default';
    }
  };

  const getRoleLabel = (role?: string) => {
    switch (role) {
      case 'client':
        return 'Client';
      case 'coursier':
        return 'Coursier';
      case 'entreprise':
        return 'Entreprise';
      case 'admin':
        return 'Administrateur';
      default:
        return 'Utilisateur';
    }
  };

  // Si l'utilisateur n'est pas connecté, ne pas afficher le profil
  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <Box>
      <IconButton
        size="large"
        aria-label="show 11 new notifications"
        color="inherit"
        aria-controls="msgs-menu"
        aria-haspopup="true"
        sx={{
          ...(typeof anchorEl2 === 'object' && {
            color: 'primary.main',
          }),
        }}
        onClick={handleClick2}
      >
        <Avatar
          src={ProfileImg}
          alt={ProfileImg}
          sx={{
            width: 35,
            height: 35,
          }}
        />
      </IconButton>
      {/* ------------------------------------------- */}
      {/* Message Dropdown */}
      {/* ------------------------------------------- */}
      <Menu
        id="msgs-menu"
        anchorEl={anchorEl2}
        keepMounted
        open={Boolean(anchorEl2)}
        onClose={handleClose2}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        sx={{
          '& .MuiMenu-paper': {
            width: '360px',
            p: 4,
          },
        }}
      >
        <Typography variant="h5">Profil Utilisateur</Typography>
        <Stack direction="row" py={3} spacing={2} alignItems="center">
          <Avatar
            src={ProfileImg}
            alt={user.name}
            sx={{ width: 95, height: 95 }}
          >
            {user.name.charAt(0).toUpperCase()}
          </Avatar>
          <Box>
            <Typography variant="subtitle2" color="textPrimary" fontWeight={600}>
              {user.name}
            </Typography>
            <Chip
              label={getRoleLabel(user.role)}
              color={getRoleColor(user.role) as any}
              size="small"
              sx={{ mb: 1 }}
            />
            <Typography
              variant="subtitle2"
              color="textSecondary"
              display="flex"
              alignItems="center"
              gap={1}
            >
              <IconMail width={15} height={15} />
              {user.email}
            </Typography>
          </Box>
        </Stack>
        <Divider />

        {/* Menu items personnalisés */}
        <Box sx={{ py: 2, px: 0 }} className="hover-text-primary">
          <Link to={`/${user.role}/profil`}>
            <Stack direction="row" spacing={2}>
              <Box
                width="45px"
                height="45px"
                bgcolor="primary.light"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <IconUser size={24} />
              </Box>
              <Box>
                <Typography
                  variant="subtitle2"
                  fontWeight={600}
                  color="textPrimary"
                  className="text-hover"
                >
                  Mon Profil
                </Typography>
                <Typography
                  color="textSecondary"
                  variant="subtitle2"
                >
                  Gérer mes informations
                </Typography>
              </Box>
            </Stack>
          </Link>
        </Box>

        <Box sx={{ py: 2, px: 0 }} className="hover-text-primary">
          <Link to={`/${user.role}/parametres`}>
            <Stack direction="row" spacing={2}>
              <Box
                width="45px"
                height="45px"
                bgcolor="primary.light"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <IconSettings size={24} />
              </Box>
              <Box>
                <Typography
                  variant="subtitle2"
                  fontWeight={600}
                  color="textPrimary"
                  className="text-hover"
                >
                  Paramètres
                </Typography>
                <Typography
                  color="textSecondary"
                  variant="subtitle2"
                >
                  Configurer mon compte
                </Typography>
              </Box>
            </Stack>
          </Link>
        </Box>
        <Box mt={2}>
          <Button
            variant="outlined"
            color="error"
            fullWidth
            startIcon={<IconLogout />}
            onClick={handleLogout}
          >
            Se déconnecter
          </Button>
        </Box>
      </Menu>
    </Box>
  );
};

export default Profile;
