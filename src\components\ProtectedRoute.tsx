import React, { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useAuthContext } from 'src/context/AuthContext';
import { Box, CircularProgress, Typography } from '@mui/material';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  allowedRoles = [], 
  redirectTo = '/auth/login' 
}) => {
  const navigate = useNavigate();
  const { user, loading, isAuthenticated } = useAuthContext();

  useEffect(() => {
    if (!loading) {
      // Si l'utilisateur n'est pas authentifié, rediriger vers la page de connexion
      if (!isAuthenticated) {
        navigate(redirectTo);
        return;
      }

      // Si des rôles spécifiques sont requis, vérifier le rôle de l'utilisateur
      if (allowedRoles.length > 0 && user?.role && !allowedRoles.includes(user.role)) {
        // Rediriger vers le dashboard approprié pour le rôle de l'utilisateur
        navigate(`/${user.role}/dashboard`);
        return;
      }
    }
  }, [user, loading, isAuthenticated, navigate, allowedRoles, redirectTo]);

  // Afficher un loader pendant la vérification
  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body1" color="textSecondary">
          Vérification des permissions...
        </Typography>
      </Box>
    );
  }

  // Si l'utilisateur n'est pas authentifié, ne rien afficher (la redirection est en cours)
  if (!isAuthenticated) {
    return null;
  }

  // Si des rôles sont spécifiés et que l'utilisateur n'a pas le bon rôle, ne rien afficher
  if (allowedRoles.length > 0 && user?.role && !allowedRoles.includes(user.role)) {
    return null;
  }

  // Afficher le contenu protégé
  return <>{children}</>;
};

export default ProtectedRoute;
