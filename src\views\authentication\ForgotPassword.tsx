import React, { useState } from 'react';
import {
  <PERSON>,
  Ty<PERSON>graphy,
  Button,
  Stack,
  TextField,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Grid,
  useTheme,
  alpha,
  InputAdornment,
} from '@mui/material';
import {
  IconMail,
  IconPlus,
  IconCircle,
  IconArrowLeft,
} from '@tabler/icons-react';
import { Link, useNavigate } from 'react-router';

const ForgotPassword = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [emailError, setEmailError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (emailError) {
      setEmailError('');
    }
    if (error) {
      setError('');
    }
  };

  const validateEmail = () => {
    if (!email) {
      setEmailError('Email is required');
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Invalid email format');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateEmail()) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSuccess(true);
    } catch (error: any) {
      setError('Failed to send reset email. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Card
          elevation={0}
          sx={{
            width: '100%',
            maxWidth: '400px',
            bgcolor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            borderRadius: 4,
            border: `1px solid ${alpha('#ffffff', 0.2)}`,
            mx: 2,
          }}
        >
          <CardContent sx={{ p: 4, textAlign: 'center' }}>
            <Box
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                bgcolor: alpha(theme.palette.success.main, 0.1),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mx: 'auto',
                mb: 3,
              }}
            >
              <IconMail size={40} color={theme.palette.success.main} />
            </Box>
            
            <Typography
              variant="h4"
              sx={{
                color: '#2d3748',
                fontWeight: 600,
                mb: 2,
              }}
            >
              Check your email
            </Typography>
            
            <Typography
              variant="body1"
              sx={{
                color: '#6b7280',
                mb: 4,
                lineHeight: 1.6,
              }}
            >
              We've sent a password reset link to <strong>{email}</strong>
            </Typography>
            
            <Button
              component={Link}
              to="/auth/login"
              variant="contained"
              fullWidth
              startIcon={<IconArrowLeft size={20} />}
              sx={{
                py: 1.5,
                borderRadius: 3,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                textTransform: 'none',
                fontSize: '1rem',
                fontWeight: 600,
                boxShadow: '0 4px 15px rgba(102, 126, 234, 0.4)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  boxShadow: '0 6px 20px rgba(102, 126, 234, 0.6)',
                },
              }}
            >
              Back to Sign In
            </Button>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `
            radial-gradient(circle at 20% 80%, ${alpha('#ffffff', 0.1)} 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, ${alpha('#ffffff', 0.1)} 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, ${alpha('#ffffff', 0.05)} 0%, transparent 50%)
          `,
        },
      }}
    >
      {/* Decorative elements */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          left: '10%',
          width: '100px',
          height: '100px',
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        <IconPlus size={24} color="white" />
      </Box>
      <Box
        sx={{
          position: 'absolute',
          top: '20%',
          right: '15%',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        {[...Array(9)].map((_, i) => (
          <Box key={i} sx={{ width: '4px', height: '4px', bgcolor: 'white', borderRadius: '50%' }} />
        ))}
      </Box>
      <Box
        sx={{
          position: 'absolute',
          bottom: '30%',
          left: '5%',
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        <IconCircle size={20} color="white" />
      </Box>

      <Grid container sx={{ height: '100vh', zIndex: 2 }}>
        {/* Left side - Welcome section */}
        <Grid
          item
          xs={12}
          md={7}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'flex-start',
            px: { xs: 4, md: 8 },
            py: 4,
            position: 'relative',
          }}
        >
          <Typography
            variant="h2"
            sx={{
              color: 'white',
              fontWeight: 700,
              fontSize: { xs: '2.5rem', md: '3.5rem' },
              mb: 2,
              lineHeight: 1.2,
            }}
          >
            Forgot your password?
          </Typography>
          <Typography
            variant="h6"
            sx={{
              color: alpha('#ffffff', 0.9),
              fontSize: '1.1rem',
              maxWidth: '400px',
              lineHeight: 1.6,
            }}
          >
            No worries! Enter your email and we'll send you a reset link.
          </Typography>
        </Grid>

        {/* Right side - Forgot password form */}
        <Grid
          item
          xs={12}
          md={5}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            px: { xs: 2, md: 4 },
            py: 4,
          }}
        >
          <Card
            elevation={0}
            sx={{
              width: '100%',
              maxWidth: '400px',
              bgcolor: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              borderRadius: 4,
              border: `1px solid ${alpha('#ffffff', 0.2)}`,
            }}
          >
            <CardContent sx={{ p: 4 }}>
              <Typography
                variant="h4"
                sx={{
                  color: '#2d3748',
                  fontWeight: 600,
                  mb: 4,
                  textAlign: 'center',
                }}
              >
                Reset Password
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              <Box component="form" onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <TextField
                    fullWidth
                    placeholder="Enter your email"
                    name="email"
                    type="email"
                    value={email}
                    onChange={handleChange}
                    error={!!emailError}
                    helperText={emailError}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <IconMail size={20} color="#9ca3af" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        bgcolor: '#f8fafc',
                        border: 'none',
                        '& fieldset': {
                          border: 'none',
                        },
                        '&:hover fieldset': {
                          border: 'none',
                        },
                        '&.Mui-focused fieldset': {
                          border: `2px solid ${theme.palette.primary.main}`,
                        },
                      },
                    }}
                  />

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    disabled={loading}
                    sx={{
                      mt: 3,
                      py: 1.5,
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      textTransform: 'none',
                      fontSize: '1rem',
                      fontWeight: 600,
                      boxShadow: '0 4px 15px rgba(102, 126, 234, 0.4)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                        boxShadow: '0 6px 20px rgba(102, 126, 234, 0.6)',
                      },
                      '&:disabled': {
                        background: '#e5e7eb',
                        color: '#9ca3af',
                        boxShadow: 'none',
                      },
                    }}
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Send Reset Link'}
                  </Button>

                  <Typography
                    variant="body2"
                    sx={{
                      textAlign: 'center',
                      color: '#6b7280',
                      mt: 2,
                    }}
                  >
                    Remember your password?{' '}
                    <Typography
                      component={Link}
                      to="/auth/login"
                      sx={{
                        color: theme.palette.primary.main,
                        textDecoration: 'none',
                        fontWeight: 600,
                        '&:hover': {
                          textDecoration: 'underline',
                        },
                      }}
                    >
                      Sign In
                    </Typography>
                  </Typography>
                </Stack>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Global styles for animations */}
      <style>
        {`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(10deg); }
          }
        `}
      </style>
    </Box>
  );
};

export default ForgotPassword;
