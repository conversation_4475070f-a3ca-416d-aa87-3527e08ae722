import { useNavigate } from "react-router-dom";
import { FaArrowLeft } from "react-icons/fa";
import { Commande } from "../../models/coursier/commande";

interface Props {
  commande: Commande;
}

const CommandeDetails = ({ commande }: Props) => {
  const navigate = useNavigate();

  const path = "/coursier/commandes"; 

  return (
    <div className="rounded-lg shadow-md p-6 mx-auto">
      <div className="flex items-center mb-4">
        <button className="text-primary mr-4" onClick={() => navigate(path)}>
          <FaArrowLeft size={24} />
        </button>
        <div>
          <h2 className="text-xl font-semibold">Commande de creation</h2>
          <p className="text-gray-500">{commande?.date_creation}</p>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">
          Les informations de client
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-gray-600">Nom complet</p>
            <p>{commande?.nom_client}</p>
          </div>
          <div>
            <p className="text-gray-600">email</p>
            <p>{commande?.email_client}</p>
          </div>
          <div>
            <p className="text-gray-600">Telephone</p>
            <p>{commande?.telephone_client}</p>
          </div>
          <div>
            <p className="text-gray-600">Adress</p>
            <p>{commande?.adresse_client}</p>
          </div>
          <div>
            <p className="text-gray-600">ville de creation</p>
            <p>{commande?.ville_client}</p>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">
          Les informations du société
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-gray-600">Nom d'entreprise</p>
            <p>{commande?.nom_societe}</p>
          </div>
          <div>
            <p className="text-gray-600">Telephone</p>
            <p>{commande?.telephone_societe}</p>
          </div>
          <div>
            <p className="text-gray-600">email</p>
            <p>{commande?.email_societe}</p>
          </div>
          <div>
            <p className="text-gray-600">Adress</p>
            <p>{commande?.adresse_societe}</p>
          </div>
          <div>
            <p className="text-gray-600">Ville</p>
            <p>{commande?.ville_societe}</p>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <button className="bg-emerald-500 text-white px-4 py-2 rounded-md hover:bg-emerald-600 transition-colors">
          Télécharger le reçus
        </button>
        <button className="bg-emerald-500 text-white px-4 py-2 rounded-md hover:bg-emerald-600 transition-colors">
          Accepter
        </button>
      </div>
    </div>
  );
};

export default CommandeDetails;
