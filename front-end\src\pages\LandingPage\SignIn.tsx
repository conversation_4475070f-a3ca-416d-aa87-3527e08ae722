import React, { useState } from 'react';
import './btnStyle.css';
import './loginStyles.css';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthContext } from '../../contexts/AuthContext';
const SignIn = () => {
    const navigate = useNavigate();
    const { login, error: authError } = useAuthContext();

    const [loginData, setLoginData] = useState({
        email: '',
        password: ''
    });
    const [loginError, setLoginError] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [loginSuccess, setLoginSuccess] = useState(false);

    const handleLoginChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setLoginData({ ...loginData, [name]: value });
        // Reset any previous errors when user is typing
        setLoginError('');
    };

    const handleLoginSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsSubmitting(true);
        setLoginError('');

        // Validation basique
        if (!loginData.email) {
            setLoginError('Veuillez entrer votre email');
            setIsSubmitting(false);
            return;
        }

        if (!loginData.password) {
            setLoginError('Veuillez entrer votre mot de passe');
            setIsSubmitting(false);
            return;
        }

        console.log('Form submitted:', loginData);

        try {
            // Utiliser la fonction login du hook useAuth
            const success = await login(loginData.email, loginData.password);

            if (success) {
                setLoginSuccess(true);

                // Récupérer le rôle de l'utilisateur depuis le contexte d'authentification
                const userRole = localStorage.getItem('user_role');
                console.log('User role from localStorage:', userRole);

                // Redirection vers le dashboard approprié après un court délai
                setTimeout(() => {
                    // Rediriger en fonction du rôle réel de l'utilisateur
                    switch(userRole) {
                        case 'admin':
                            navigate('/admin/dashboard');
                            break;
                        case 'entreprise':
                            navigate('/entreprise/dashboard');
                            break;
                        case 'coursier':
                            navigate('/coursier/dashboard');
                            break;
                        case 'client':
                            navigate('/client/dashboard');
                            break;
                        default:
                            // Par défaut, rediriger vers le dashboard client
                            navigate('/client/dashboard');
                    }
                }, 1000);
            } else {
                setLoginError(authError || 'Identifiants incorrects');
            }
        } catch (error) {
            console.error('Login error:', error);
            setLoginError('Une erreur est survenue lors de la connexion');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="flex h-screen">
            {/* Left section - Light blue background with cybersecurity icon */}
            <div className="hidden md:flex md:w-1/2 bg-[#97afc6] relative overflow-hidden" style={{ borderTopRightRadius: '35px' }}>
                <div className="absolute inset-0 bg-[#d3e9ff] opacity-50"></div>

                {/* Cybersecurity icon with animation */}
                <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <img
                        src="/src/assets/security.png"
                        alt="Charikti"
                        className="w-64 h-60 security-animation"
                        style={{
                            filter: "drop-shadow(0 0 10px rgba(255, 255, 255, 0.5))"
                        }}
                    />
                </div>

                {/* Text */}
                <div className="absolute top-20 left-20 text-white z-10">
                    <p className="text-xl opacity-80">Connexion sécurisée — retrouvez vos services en un clic !</p>
                </div>
            </div>

            {/* Right section - Login form */}
            <div className="w-full md:w-1/2 bg-white Sp-8 flex flex-col justify-center">
                <div className="max-w-md mx-auto w-full">
                    <h2 className="text-2xl font-semibold text-gray-800 mb-1">CONNEXION</h2>
                    <br></br>
                    <form onSubmit={handleLoginSubmit} className="space-y-6">
                        {/* Email/Username field */}
                        <div>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                    </svg>
                                </div>
                                <input
                                    type="email"
                                    name="email"
                                    value={loginData.email}
                                    onChange={handleLoginChange}
                                    className="bg-gray-100 text-gray-900 rounded-lg block w-full pl-10 p-2.5 focus:outline-none focus:ring-2 focus:ring-[#5b99c2]"
                                    placeholder="Email"
                                    required
                                />
                            </div>
                        </div>

                        {/* Password field */}
                        <div>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd"></path>
                                    </svg>
                                </div>
                                <input
                                    type="password"
                                    name="password"
                                    value={loginData.password}
                                    onChange={handleLoginChange}
                                    className="bg-gray-100 text-gray-900 rounded-lg block w-full pl-10 p-2.5 focus:outline-none focus:ring-2 focus:ring-[#5b99c2]"
                                    placeholder="Mot de passe"
                                    required
                                />
                            </div>
                        </div>

                        {/* Remember me & Forgot password */}
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <input
                                    id="remember"
                                    type="checkbox"
                                    className="w-4 h-4 text-[#5b99c2] bg-gray-100 border-gray-300 rounded focus:ring-[#5b99c2]"
                                />
                                <label htmlFor="remember" className="ml-2 text-sm text-gray-600">Se souvenir de moi</label>
                            </div>
                            <Link to="" className="text-sm text-[#5b99c2] hover:text-[#4a7da0]">Mot de passe oublié ?</Link>
                        </div>



                        {/* Error message */}
                        {loginError && (
                            <div className="text-red-500 text-sm text-center">
                                {loginError}
                            </div>
                        )}

                        {/* Success message */}
                        {loginSuccess && (
                            <div className="text-green-500 text-sm text-center">
                                Connexion réussie ! Redirection vers votre dashboard en cours...
                                {localStorage.getItem('user_role') && (
                                    <div className="text-blue-500 text-xs mt-1">
                                        Vous êtes connecté en tant que {localStorage.getItem('user_role') === 'entreprise' ? 'entreprise' :
                                                                      localStorage.getItem('user_role') === 'coursier' ? 'coursier' :
                                                                      localStorage.getItem('user_role') === 'admin' ? 'administrateur' : 'client'}
                                    </div>
                                )}
                            </div>
                        )}

                        {/* Login button */}
                        <button
                            type="submit"
                            disabled={isSubmitting}
                            className="w-full bg-[#5b99c2] hover:bg-[#4a7da0] text-white font-medium rounded-lg py-2.5 text-center transition duration-200 ease-in-out"
                        >
                            {isSubmitting ? 'Connexion en cours...' : 'CONNEXION'}
                        </button>
                    </form>

                    {/* Create account link */}
                    <div className="text-center mt-6">
                        <p className="text-sm text-gray-600">
                            Vous n'avez pas de compte ?{' '}
                            <Link to="/signup" className="text-[#5b99c2] hover:text-[#4a7da0] font-medium">
                                Créer un compte
                            </Link>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SignIn;