<?php
// Script pour créer un utilisateur administrateur

// Paramètres de connexion à la base de données
$host = 'localhost';
$db = 'charikti';  // Remplacez par le nom de votre base de données
$user = 'root';    // Remplacez par votre nom d'utilisateur MySQL
$pass = '';        // Remplacez par votre mot de passe MySQL
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    // Connexion à la base de données
    $pdo = new PDO($dsn, $user, $pass, $options);
    
    // Données de l'administrateur
    $name = 'Admin';
    $email = '<EMAIL>';
    $password = password_hash('admin123', PASSWORD_BCRYPT); // Mot de passe hashé
    $role = 'admin';
    
    // Vérifier si l'utilisateur existe déjà
    $stmt = $pdo->prepare('SELECT * FROM users WHERE email = ?');
    $stmt->execute([$email]);
    $existingUser = $stmt->fetch();
    
    if ($existingUser) {
        // Mettre à jour l'utilisateur existant pour le rendre admin
        $stmt = $pdo->prepare('UPDATE users SET role = ? WHERE email = ?');
        $stmt->execute([$role, $email]);
        echo "L'utilisateur $email a été mis à jour avec le rôle 'admin'.\n";
    } else {
        // Créer un nouvel utilisateur admin
        $stmt = $pdo->prepare('INSERT INTO users (name, email, password, role, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())');
        $stmt->execute([$name, $email, $password, $role]);
        echo "Un nouvel utilisateur admin a été créé avec l'email $email et le mot de passe 'admin123'.\n";
    }
    
} catch (\PDOException $e) {
    echo "Erreur de base de données: " . $e->getMessage() . "\n";
    exit(1);
}
