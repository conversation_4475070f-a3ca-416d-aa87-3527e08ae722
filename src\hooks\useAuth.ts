import { useState, useEffect } from 'react';
import api from '../services/api-client';

interface User {
  id: number;
  name: string;
  email: string;
  role: 'client' | 'coursier' | 'entreprise' | 'admin';
  profile?: any;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  role: 'client' | 'coursier' | 'entreprise' | 'admin';
}

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fonction pour récupérer les informations de l'utilisateur connecté
  const fetchUser = async () => {
    try {
      setLoading(true);
      const response = await api.get<User>('/user');
      setUser(response.data);
      setError(null);
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'utilisateur:', error);
      setUser(null);
      // Ne pas définir d'erreur ici car l'utilisateur peut ne pas être connecté
    } finally {
      setLoading(false);
    }
  };

  // Fonction d'inscription
  const register = async (userData: RegisterData): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.post('/register', userData);
      
      if (response.data.token) {
        localStorage.setItem('token', response.data.token);
        api.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
        await fetchUser();
        return true;
      }
      
      return false;
    } catch (error: any) {
      console.error('Erreur lors de l\'inscription:', error);
      setError(error.response?.data?.message || 'Erreur lors de l\'inscription');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Fonction de connexion
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.post('/login', { email, password });
      
      if (response.data.token) {
        localStorage.setItem('token', response.data.token);
        api.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
        await fetchUser();
        return true;
      }
      
      return false;
    } catch (error: any) {
      console.error('Erreur lors de la connexion:', error);
      setError(error.response?.data?.message || 'Erreur lors de la connexion');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Fonction de déconnexion
  const logout = async () => {
    try {
      await api.post('/logout');
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    } finally {
      localStorage.removeItem('token');
      delete api.defaults.headers.common['Authorization'];
      setUser(null);
    }
  };

  // Vérifier si l'utilisateur est connecté au chargement
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      fetchUser();
    } else {
      setLoading(false);
    }
  }, []);

  return {
    user,
    loading,
    error,
    register,
    login,
    logout,
    fetchUser,
    isAuthenticated: !!user,
  };
};

export default useAuth;
