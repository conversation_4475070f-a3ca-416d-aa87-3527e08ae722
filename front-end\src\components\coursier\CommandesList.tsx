import { Commande } from "../../models/coursier/commande";
import CommandeItem from "./CommandeItem";



interface Props {
    commandes: Commande[];
}


const CommandesList = ({ commandes }: Props) => {
  return (
    <div className="container mx-auto">
      <h2 className="text-2xl font-bold mb-4 text-primary">Commandes</h2>
      {commandes.map((commande, index) => (
        <CommandeItem key={index} commande={commande} />
      ))}
    </div>
  );
};

export default CommandesList;
