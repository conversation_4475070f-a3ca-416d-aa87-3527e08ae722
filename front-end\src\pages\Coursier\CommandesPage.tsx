import CommandesList from "../../components/coursier/CommandesList";
import InnerPageContainer from "../../components/InnerPageContainer";
import useAvailableCommandes from "../../hooks/coursier/useAvailableCommandes";

const CommandesPage = () => {
  const { data: commandes, isLoading, error } = useAvailableCommandes();

  return (
    <InnerPageContainer>
      {isLoading && <p>Loading...</p>}
      {error && <p>Error: {error.message || "An error occurred"}</p>}
      {commandes && <CommandesList commandes={commandes} />}
    </InnerPageContainer>
  );
};

export default CommandesPage;
