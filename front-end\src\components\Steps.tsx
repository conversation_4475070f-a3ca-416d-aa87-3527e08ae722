import React from 'react';

const Steps = ({ steps, currentStep }) => {
    return (
        <ul className="steps">
            {steps.map((step, index) => (
                <li
                    key={index}
                    className={`step ${index <= currentStep ? "step-primary" : ""}`}>
                    {step}
                </li>
            ))}
        </ul>
    );
};

export default Steps;
