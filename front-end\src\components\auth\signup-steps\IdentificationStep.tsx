import React, { useState } from 'react';
import { SignUpFormData } from '../VerticalSignUpForm';
import { FaEye, FaEyeSlash } from 'react-icons/fa';

interface IdentificationStepProps {
  formData: SignUpFormData;
  updateFormData: (data: Partial<SignUpFormData>) => void;
  onSubmit: (e: React.FormEvent) => void;
  onPrevious: () => void;
}

const IdentificationStep: React.FC<IdentificationStepProps> = ({ 
  formData, 
  updateFormData, 
  onSubmit, 
  onPrevious 
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    updateFormData({ [name]: value });
    
    // Check password match when confirm password is changed
    if (name === 'confirmPassword' || name === 'password') {
      if (name === 'password' && formData.confirmPassword && value !== formData.confirmPassword) {
        setPasswordError('Passwords do not match');
      } else if (name === 'confirmPassword' && value !== formData.password) {
        setPasswordError('Passwords do not match');
      } else {
        setPasswordError('');
      }
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const isFormValid = () => {
    return (
      formData.password.trim() !== '' &&
      formData.confirmPassword.trim() !== '' &&
      formData.password === formData.confirmPassword &&
      formData.password.length >= 8
    );
  };

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">Security Information</h2>
      <p className="text-gray-600 mb-8">
        Create a secure password for your account
      </p>
      
      <form onSubmit={onSubmit}>
        <div className="space-y-4 mb-8">
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 pr-10"
                placeholder="At least 8 characters"
                minLength={8}
                required
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
                onClick={togglePasswordVisibility}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Password must be at least 8 characters long
            </p>
          </div>
          
          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
              Confirm Password
            </label>
            <div className="relative">
              <input
                type={showConfirmPassword ? "text" : "password"}
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 pr-10 ${
                  passwordError ? 'border-red-500' : 'border-gray-300'
                }`}
                required
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
                onClick={toggleConfirmPasswordVisibility}
              >
                {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {passwordError && (
              <p className="text-xs text-red-500 mt-1">{passwordError}</p>
            )}
          </div>
          
          <div className="flex items-center mt-4">
            <input
              type="checkbox"
              id="terms"
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label htmlFor="terms" className="ml-2 block text-sm text-gray-700">
              I agree to the <a href="#" className="text-indigo-600 hover:underline">Terms and Conditions</a>
            </label>
          </div>
        </div>
        
        <div className="flex justify-between">
          <button
            type="button"
            onClick={onPrevious}
            className="py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Back
          </button>
          
          <button
            type="submit"
            disabled={!isFormValid()}
            className={`py-2 px-4 rounded-md text-white font-medium ${
              isFormValid() 
                ? 'bg-indigo-600 hover:bg-indigo-700' 
                : 'bg-gray-300 cursor-not-allowed'
            }`}
          >
            Create Account
          </button>
        </div>
      </form>
    </div>
  );
};

export default IdentificationStep;
