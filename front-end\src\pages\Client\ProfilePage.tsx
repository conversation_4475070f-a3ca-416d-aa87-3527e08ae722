import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON> } from 'react-icons/fa';
import { clientProfileService, ClientProfile } from '../../services/profileService';
import { useAuthContext } from '../../contexts/AuthContext';
import { authService } from '../../services/authService';
import InnerPageContainer from '../../components/InnerPageContainer';

const ProfilePage = () => {
  const [profileData, setProfileData] = useState<ClientProfile>({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    address: '',
    companyName: '',
  });

  // États pour le formulaire de profil
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { updateProfileImage } = useAuthContext();

  // États pour le changement de mot de passe
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
  const [changingPassword, setChangingPassword] = useState(false);

  // Charger les données du profil au chargement du composant
  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        setLoading(true);

        // Récupérer les données du profil
        const data = await clientProfileService.getProfile();
        console.log('Données du profil récupérées:', data);

        // Si les données sont vides, essayer de récupérer directement depuis l'API Laravel
        if (!data.firstName && !data.lastName) {
          try {
            const response = await fetch('http://localhost:8000/api/clients/profile', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              }
            });

            if (response.ok) {
              const apiData = await response.json();
              console.log('Données du profil depuis l\'API Laravel:', apiData);

              // Convertir le format de l'API Laravel au format attendu par l'application
              const convertedData = {
                id: apiData.id,
                firstName: apiData.first_name || '',
                lastName: apiData.last_name || '',
                email: apiData.email || '',
                phoneNumber: apiData.phone_number || '',
                address: apiData.address || '',
                companyName: apiData.company_name || '',
                profileImage: apiData.profile_image || null
              };

              setProfileData(convertedData);

              if (apiData.profile_image) {
                // Construire l'URL complète de l'image
                const imageUrl = `http://localhost:8000/storage/${apiData.profile_image}`;
                console.log('URL de l\'image de profil:', imageUrl);
                setProfileImage(imageUrl);
              }
            }
          } catch (apiError) {
            console.error('Erreur lors de la récupération depuis l\'API Laravel:', apiError);
          }
        } else {
          setProfileData(data);

          // Vérifier s'il y a une image stockée dans le localStorage
          const storedImage = localStorage.getItem('client_profile_image');
          if (storedImage && storedImage.startsWith('data:image')) {
            console.log('Image récupérée depuis localStorage');
            setProfileImage(storedImage);
          } else if (data.profileImage) {
            console.log('Image récupérée depuis les données du profil');
            setProfileImage(data.profileImage);
          }
        }

        setError(null);
      } catch (err) {
        setError('Erreur lors du chargement des données du profil');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Réinitialiser les états
      setError(null);
      setSuccess(null);
      setSaving(true);

      // Utiliser FileReader pour convertir l'image en base64
      const reader = new FileReader();

      reader.onload = (event) => {
        if (event.target && event.target.result) {
          // Obtenir l'image en base64
          const base64Image = event.target.result as string;

          // Afficher l'image immédiatement
          setProfileImage(base64Image);

          // Mettre à jour les données du profil
          setProfileData(prev => ({
            ...prev,
            profileImage: base64Image
          }));

          // Mettre à jour l'image dans le contexte d'authentification
          // Cette fonction stockera également l'image dans le localStorage
          console.log('Mise à jour de l\'image de profil dans le contexte d\'authentification');
          const success = updateProfileImage(base64Image);
          console.log('Mise à jour réussie:', success);

          // Afficher un message de succès
          setSuccess('Image de profil mise à jour avec succès !');

          // Terminer le chargement
          setSaving(false);
        }
      };

      reader.onerror = () => {
        setError('Erreur lors de la lecture du fichier');
        setSaving(false);
      };

      // Lire le fichier en tant que Data URL (base64)
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSaving(true);
      setSuccess(null);
      setError(null);

      // Essayer d'abord de mettre à jour via l'API Laravel
      try {
        // Convertir les données au format attendu par l'API Laravel
        const apiData = {
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          email: profileData.email,
          phone_number: profileData.phoneNumber,
          address: profileData.address,
          company_name: profileData.companyName
        };

        // Récupérer l'ID du client depuis les données du profil
        const clientId = profileData.id;

        if (clientId) {
          const response = await fetch(`http://localhost:8000/api/clients/${clientId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(apiData)
          });

          if (response.ok) {
            console.log('Profil mis à jour via l\'API Laravel');
          } else {
            console.error('Erreur lors de la mise à jour via l\'API Laravel:', await response.json());
            throw new Error('Erreur lors de la mise à jour via l\'API Laravel');
          }
        } else {
          throw new Error('ID du client non disponible');
        }
      } catch (apiError) {
        console.error('Erreur lors de la mise à jour via l\'API Laravel:', apiError);
        // Fallback à l'API mock
        await clientProfileService.updateProfile(profileData);
      }

      setSuccess('Profil mis à jour avec succès !');

      // Faire défiler vers le haut pour voir le message de succès
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } catch (err) {
      setError('Erreur lors de la mise à jour du profil');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  // Gérer le changement des champs de mot de passe
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));

    // Réinitialiser les messages d'erreur et de succès
    setPasswordError(null);
    setPasswordSuccess(null);
  };

  // Gérer la soumission du formulaire de changement de mot de passe
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Réinitialiser les messages
    setPasswordError(null);
    setPasswordSuccess(null);

    // Vérifier que les mots de passe correspondent
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setPasswordError('Les mots de passe ne correspondent pas');
      return;
    }

    // Vérifier que le nouveau mot de passe n'est pas vide
    if (!passwordData.newPassword) {
      setPasswordError('Le nouveau mot de passe ne peut pas être vide');
      return;
    }

    try {
      setChangingPassword(true);

      // Appeler le service pour changer le mot de passe
      const success = await authService.changePassword({
        email: profileData.email,
        oldPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword
      });

      if (success) {
        setPasswordSuccess('Mot de passe changé avec succès');

        // Réinitialiser le formulaire
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        setPasswordError('Erreur lors du changement de mot de passe');
      }
    } catch (err: any) {
      setPasswordError(err.message || 'Erreur lors du changement de mot de passe');
      console.error(err);
    } finally {
      setChangingPassword(false);
    }
  };

  return (
    <InnerPageContainer title="Profil Client">
      {/* Afficher un message de succès s'il y en a un */}
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
          <FaCheckCircle className="mr-2" />
          <p>{success}</p>
        </div>
      )}

      {/* Afficher un message d'erreur s'il y en a un */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {/* Afficher un indicateur de chargement pendant le chargement des données */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <FaSpinner className="animate-spin text-blue-600 text-4xl" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Colonne de gauche - Photo de profil */}
          <div className="md:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Photo de profil</h2>

              <div className="flex flex-col items-center">
                <div className="relative mb-4">
                  <div className="w-40 h-40 rounded-full overflow-hidden border-4 border-gray-200">
                    {profileImage ? (
                      <img
                        src={profileImage}
                        alt="Profile"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          console.error('Erreur de chargement de l\'image:', e);
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-300 flex items-center justify-center text-gray-500">
                        <span className="text-4xl font-bold">
                          {profileData.firstName && profileData.lastName
                            ? `${profileData.firstName.charAt(0)}${profileData.lastName.charAt(0)}`
                            : 'C'}
                        </span>
                      </div>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full shadow-md hover:bg-blue-700 transition-colors"
                  >
                    <FaCamera />
                  </button>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleImageChange}
                    accept="image/*"
                    className="hidden"
                  />
                </div>

                <div className="text-center">
                  <h3 className="text-lg font-semibold">
                    {profileData.firstName} {profileData.lastName}
                  </h3>
                  <p className="text-gray-600">{profileData.email}</p>
                  <div className="mt-2 inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">
                    Client
                  </div>
                </div>
              </div>

              {/* Informations supplémentaires */}
              <div className="mt-6">
                <div className="border-t pt-4">
                  {profileData.companyName && (
                    <p className="text-sm text-gray-600 mb-1">
                      <span className="font-semibold">Entreprise:</span>{' '}
                      {profileData.companyName}
                    </p>
                  )}
                  {profileData.phoneNumber && (
                    <p className="text-sm text-gray-600">
                      <span className="font-semibold">Téléphone:</span>{' '}
                      {profileData.phoneNumber}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Colonne de droite - Informations personnelles et sécurité */}
          <div className="md:col-span-2">
            {/* Informations personnelles */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Informations personnelles</h2>

              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                      Prénom
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={profileData.firstName}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                      Nom
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={profileData.lastName}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={profileData.email}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                    Numéro de téléphone
                  </label>
                  <input
                    type="tel"
                    id="phoneNumber"
                    name="phoneNumber"
                    value={profileData.phoneNumber}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                    Adresse
                  </label>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    value={profileData.address}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-1">
                    Nom de l'entreprise (optionnel)
                  </label>
                  <input
                    type="text"
                    id="companyName"
                    name="companyName"
                    value={profileData.companyName}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Enregistrement...
                    </>
                  ) : (
                    'Enregistrer les modifications'
                  )}
                </button>
              </form>
            </div>

            {/* Sécurité - Changement de mot de passe */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-4">
                <FaKey className="text-blue-600 mr-2" />
                <h2 className="text-xl font-semibold">Sécurité</h2>
              </div>

              {/* Afficher un message de succès pour le mot de passe s'il y en a un */}
              {passwordSuccess && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
                  <FaCheckCircle className="mr-2" />
                  <p>{passwordSuccess}</p>
                </div>
              )}

              {/* Afficher un message d'erreur pour le mot de passe s'il y en a un */}
              {passwordError && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                  <p>{passwordError}</p>
                </div>
              )}

              <form onSubmit={handlePasswordSubmit}>
                <div className="mb-4">
                  <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Mot de passe actuel
                  </label>
                  <input
                    type="password"
                    id="currentPassword"
                    name="currentPassword"
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Nouveau mot de passe
                  </label>
                  <input
                    type="password"
                    id="newPassword"
                    name="newPassword"
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Confirmer le nouveau mot de passe
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
                  disabled={changingPassword}
                >
                  {changingPassword ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Modification en cours...
                    </>
                  ) : (
                    'Changer le mot de passe'
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      )}
    </InnerPageContainer>
  );
};

export default ProfilePage;
