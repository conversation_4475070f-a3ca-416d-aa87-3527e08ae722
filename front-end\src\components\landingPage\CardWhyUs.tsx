
interface feature{
    title: string;
    description: string;
    icon: string;
}

const CardWhyUs= (features: feature)=>{

    return (
        <div className="card w-[365.2px]">
            <figure>
                <img
                    src={features.icon}
                    alt={features.title} />
            </figure>
            <div className="card-body">
                <h2 className="card-title text-[#197FCE]">
                    {features.title}
                </h2>
                <p className="text-black">{features.description}</p>
            </div>
        </div>
    );
}

export default CardWhyUs;