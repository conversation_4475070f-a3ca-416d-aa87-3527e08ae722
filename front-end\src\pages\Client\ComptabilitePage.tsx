import React from 'react';

const ComptabilitePage = () => {
  // Exemple de transactions
  const transactions = [
    {
      id: 1,
      date: "2023-06-15",
      description: "Paiement mensuel",
      montant: -250,
      statut: "Complété"
    },
    {
      id: 2,
      date: "2023-06-01",
      description: "Remboursement frais de dossier",
      montant: 75,
      statut: "Complété"
    },
    {
      id: 3,
      date: "2023-05-15",
      description: "Paiement mensuel",
      montant: -250,
      statut: "Complété"
    },
    {
      id: 4,
      date: "2023-07-15",
      description: "Paiement mensuel",
      montant: -250,
      statut: "En attente"
    }
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Comptabilité</h1>
      <p className="mb-6">Gérez vos finances et consultez l'historique de vos transactions.</p>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Solde actuel</h3>
          <p className="text-2xl font-bold text-green-600">1,250 DH</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Paiements en attente</h3>
          <p className="text-2xl font-bold text-orange-500">250 DH</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700">Total payé</h3>
          <p className="text-2xl font-bold text-blue-600">3,750 DH</p>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <h2 className="p-4 bg-gray-50 border-b text-xl font-semibold">Historique des transactions</h2>
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {transactions.map(transaction => (
              <tr key={transaction.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.date}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{transaction.description}</td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${transaction.montant < 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {transaction.montant} DH
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    transaction.statut === 'Complété' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {transaction.statut}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ComptabilitePage;
