<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('entreprises', function (Blueprint $table) {
            $table->id();
            $table->string('denomination');
            $table->string('email')->unique();
            $table->string('phone_number');
            $table->string('siege_social');
            $table->string('forme_juridique');
            $table->string('activite');
            $table->string('n_rc');
            $table->string('n_cnss');
            $table->string('i_fiscale');
            $table->string('tax_professionel');
            $table->string('profile_image')->nullable();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('entreprises');
    }
};
