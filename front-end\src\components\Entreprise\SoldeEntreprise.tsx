import { SoldeCard } from "./SoldeCard";


const solde=[
        {
                title:"total net", icon: ""
        },
        {
                title:"Commandes bloquer", icon: ""
        },
        {
                title:"commande totales", icon: ""
        },


]
export const SoldeEntreprise = (price:number)=> {

        return (
                <div className="flex flex-col">
                        <h1> Solde</h1>
                        <div className="flex flex-row">
                                {solde.map(
                                        (solde, index) =>
                                        <div key={index} className="flex flex-col w-1/3">
                
                                                <SoldeCard key={index} title={solde.title}
                                                 price={price} icon={""} />
                                        </div>
                                )
                                        }
                        </div>
                </div>
        );
}