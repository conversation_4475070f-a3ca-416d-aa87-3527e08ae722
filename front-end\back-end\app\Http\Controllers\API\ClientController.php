<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ClientController extends Controller
{
    /**
     * Display a listing of the clients.
     */
    public function index()
    {
        $clients = Client::all();
        return response()->json($clients);
    }

    /**
     * Store a newly created client in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:clients',
            'phone_number' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'user_id' => 'nullable|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Handle profile image upload
        $profileImagePath = null;
        if ($request->hasFile('profile_image')) {
            $profileImagePath = $request->file('profile_image')->store('profile_images/clients', 'public');
        }

        // Si l'utilisateur est authentifié, utiliser son ID, sinon utiliser l'ID fourni dans la requête
        $userId = Auth::id() ?? $request->user_id;

        // Log pour le débogage
        Log::info('Creating client profile', [
            'auth_id' => Auth::id(),
            'request_user_id' => $request->user_id,
            'using_user_id' => $userId
        ]);

        $client = Client::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone_number' => $request->phone_number,
            'address' => $request->address,
            'company_name' => $request->company_name,
            'profile_image' => $profileImagePath,
            'user_id' => $userId,
        ]);

        return response()->json($client, 201);
    }

    /**
     * Display the specified client.
     */
    public function show(Client $client)
    {
        return response()->json($client);
    }

    /**
     * Get the authenticated client's profile.
     */
    public function profile()
    {
        $user = Auth::user();
        $client = $user->client;

        if (!$client) {
            return response()->json(['message' => 'Client profile not found'], 404);
        }

        return response()->json($client);
    }

    /**
     * Update the specified client in storage.
     */
    public function update(Request $request, Client $client)
    {
        // Check if the authenticated user owns this client profile
        if (Auth::id() !== $client->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'first_name' => 'sometimes|required|string|max:255',
            'last_name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|string|email|max:255|unique:clients,email,' . $client->id,
            'phone_number' => 'sometimes|required|string|max:20',
            'address' => 'sometimes|required|string|max:255',
            'company_name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $client->update($request->only([
            'first_name',
            'last_name',
            'email',
            'phone_number',
            'address',
            'company_name',
        ]));

        return response()->json($client);
    }

    /**
     * Update the client's profile image.
     */
    public function updateProfileImage(Request $request, Client $client)
    {
        // Check if the authenticated user owns this client profile
        if (Auth::id() !== $client->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Delete old profile image if exists
        if ($client->profile_image) {
            Storage::disk('public')->delete($client->profile_image);
        }

        // Store new profile image
        $profileImagePath = $request->file('profile_image')->store('profile_images/clients', 'public');
        $client->profile_image = $profileImagePath;
        $client->save();

        return response()->json([
            'message' => 'Profile image updated successfully',
            'profile_image' => $profileImagePath
        ]);
    }

    /**
     * Remove the specified client from storage.
     */
    public function destroy(Client $client)
    {
        // Check if the authenticated user owns this client profile
        if (Auth::id() !== $client->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Delete profile image if exists
        if ($client->profile_image) {
            Storage::disk('public')->delete($client->profile_image);
        }

        $client->delete();

        return response()->json(['message' => 'Client deleted successfully']);
    }
}
