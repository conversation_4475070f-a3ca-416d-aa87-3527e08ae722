import { Link } from "react-router-dom";

interface service{
    title: string;
    description: string;
    icon: string;
}

const ServiceCard = ({ title, description, icon }:service) => {
    return (
        <div className="flex flex-col z-20  bg-[#86A8C3] items-center self-stretch px-10 pt-2  pb-10 w-80 bg-colorBgCard  rounded-[35px] max-md:px-5">
            <div className="flex gap-2.5 items-center p-2.5 max-w-full w-[150px]">
                <img loading="lazy" src={icon} alt="" className="object-contain self-stretch my-auto aspect-square rounded-[90px_90px_0px_0px] w-[180px]" />
            </div>
            <div className="flex flex-col justify-between self-stretch mt-9 w-full text-center">
                <div className="flex flex-col justify-center w-full">
                    <h3 className="self-center max-w-full text-xl font-bold leading-tight text-white whitespace-nowrap rounded-none w-[193px]">
                        {title}
                    </h3>
                    <p className="mt-7 text-lg leading-7 text-white">
                        {description}
                    </p>
                </div>
            </div>
            <div className="flex justify-center">
                <Link className="overflow-hidden gap-2.5 self-stretch px-5 py-4 mt-9 max-w-full text-lg font-bold leading-tight text-center text-white rounded-xl bg-slate-600 min-h-[54px] w-[146px]"
                 to={'/signup'}>
                    voir Plus
                </Link>
            </div>

        </div>
    );
};

export default ServiceCard;