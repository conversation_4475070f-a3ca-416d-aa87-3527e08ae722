<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Cybersecurity</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#5b99c2" offset="0%"></stop>
            <stop stop-color="#4a7da0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#E0E0E0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Cybersecurity" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Shield Base -->
        <path d="M100,20 C120,30 140,35 160,40 C160,90 150,140 100,180 C50,140 40,90 40,40 C60,35 80,30 100,20 Z" id="Shield" fill="url(#linearGradient-1)"></path>
        
        <!-- Lock Body -->
        <rect id="Lock-Body" fill="#FFFFFF" x="70" y="90" width="60" height="50" rx="5"></rect>
        
        <!-- Lock Shackle -->
        <path d="M85,90 L85,70 C85,55 115,55 115,70 L115,90" id="Lock-Shackle" stroke="#FFFFFF" stroke-width="10" stroke-linecap="round"></path>
        
        <!-- Keyhole -->
        <circle id="Keyhole-Top" fill="#4a7da0" cx="100" cy="110" r="8"></circle>
        <rect id="Keyhole-Bottom" fill="#4a7da0" x="96" y="110" width="8" height="15" rx="2"></rect>
        
        <!-- Digital Circuit Lines -->
        <path d="M60,60 L80,60 L80,80" id="Circuit-1" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"></path>
        <path d="M140,60 L120,60 L120,80" id="Circuit-2" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"></path>
        <path d="M60,140 L80,140 L80,120" id="Circuit-3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"></path>
        <path d="M140,140 L120,140 L120,120" id="Circuit-4" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"></path>
        
        <!-- Shield Highlight -->
        <path d="M100,30 C115,38 130,42 145,45 C145,85 137.5,125 100,155 C62.5,125 55,85 55,45 C70,42 85,38 100,30 Z" id="Shield-Highlight" stroke="#FFFFFF" stroke-width="2" opacity="0.5"></path>
    </g>
</svg>
