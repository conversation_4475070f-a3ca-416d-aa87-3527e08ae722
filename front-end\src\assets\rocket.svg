<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Rocket</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#E0E0E0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#5b99c2" offset="0%"></stop>
            <stop stop-color="#4a7da0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Rocket" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <path d="M100,20 C130,20 150,40 160,80 C170,120 160,160 140,180 L60,180 C40,160 30,120 40,80 C50,40 70,20 100,20 Z" id="Body" fill="url(#linearGradient-1)"></path>
        <path d="M100,20 C85,20 75,30 70,50 L130,50 C125,30 115,20 100,20 Z" id="Top" fill="url(#linearGradient-2)"></path>
        <circle id="Window" fill="#5b99c2" cx="100" cy="90" r="15"></circle>
        <path d="M60,180 L80,200 L120,200 L140,180 L60,180 Z" id="Bottom" fill="#9E9E9E"></path>
        <path d="M70,180 L60,200 L80,200 L70,180 Z" id="Left-Fin" fill="url(#linearGradient-2)"></path>
        <path d="M130,180 L120,200 L140,200 L130,180 Z" id="Right-Fin" fill="url(#linearGradient-2)"></path>
        <path d="M85,180 L75,210 L85,205 L95,210 L85,180 Z" id="Left-Flame" fill="#FF9800" opacity="0.8"></path>
        <path d="M115,180 L105,210 L115,205 L125,210 L115,180 Z" id="Right-Flame" fill="#FF9800" opacity="0.8"></path>
    </g>
</svg>
