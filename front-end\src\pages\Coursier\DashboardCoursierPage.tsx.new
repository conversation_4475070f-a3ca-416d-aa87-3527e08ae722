import { useState } from "react";
import { FaLifeRing, FaMoneyBillWave, FaClipboardList, FaHome } from "react-icons/fa";
import { Outlet } from "react-router-dom";

import NavBar from "../../components/NavBar";
import CoursierSidebar from "../../components/Coursier/CoursierSidebar";

// Define the sections of the sidebar
const sections = [
  { icon: FaHome, label: "Dashboard", link: "/coursier/dashboard" },
  { icon: FaClipboardList, label: "Commandes", link: "/coursier/commandes" },
  { icon: FaMoneyBillWave, label: "L'argent", link: "/coursier/argent" },
  { icon: FaLifeRing, label: "Support", link: "/coursier/support" },
];

const DashboardCoursier = () => {
  const [currentUrl, setCurrentUrl] = useState("/coursier/dashboard");
  const [isLoading, setIsLoading] = useState(false);
  
  const handleLinkClick = (link: string) => {
    if (link !== currentUrl) {
      setIsLoading(true);
      setCurrentUrl(link);
    }
  };
  
  // Reset loading state when iframe loads
  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  return (
    <div>
      <NavBar title="Dashboard Coursier" />
      <div className="flex h-screen">
        {/* Sidebar */}
        <CoursierSidebar 
          sections={sections} 
          onLinkClick={handleLinkClick} 
        />
        
        {/* Main Content */}
        <div className="flex-1 p-4">
          {/* Default content when on dashboard */}
          {currentUrl === "/coursier/dashboard" && (
            <div className="flex-1 p-4">
              <Outlet />
            </div>
          )}
          
          {/* Iframe for other pages */}
          {currentUrl !== "/coursier/dashboard" && (
            <div className="w-full h-[calc(100vh-120px)] bg-white rounded-lg shadow-md overflow-hidden relative">
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75 z-10">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                </div>
              )}
              <iframe 
                src={currentUrl}
                className="w-full h-full border-none"
                title="Content"
                sandbox="allow-same-origin allow-scripts allow-forms"
                loading="lazy"
                onLoad={handleIframeLoad}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardCoursier;
