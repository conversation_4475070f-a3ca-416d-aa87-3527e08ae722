{"name": "starterkit-vite-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@casl/react": "^3.1.0", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.1", "@mui/lab": "^7.0.0-beta.10", "@mui/material": "^7.0.1", "@svgr/rollup": "8.1.0", "@tabler/icons-react": "^2.39.0", "i18next": "^23.5.1", "lodash": "^4.17.21", "react": "19.0.0-rc-02c0e824-20241028", "react-dom": "19.0.0-rc-02c0e824-20241028", "react-helmet": "^6.1.0", "react-i18next": "^13.2.2", "react-router": "^7.0.2", "react-top-loading-bar": "^2.3.1", "simplebar": "^6.2.7", "simplebar-react": "^3.2.4", "stylis-plugin-rtl": "^2.1.1", "yup": "^0.32.11"}, "devDependencies": {"@types/node": "22.10.1", "@types/react": "19.0.1", "@types/react-dom": "^19.0.1", "@types/react-helmet": "^6.1.11", "@vitejs/plugin-react": "^4.3.4", "eslint": "latest", "eslint-config-next": "latest", "typescript": "^5.8.3", "vite": "^6.3.5"}}