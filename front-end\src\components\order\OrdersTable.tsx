import { useState } from "react";
import TableHeader from "./TableHeader";
import Pagination from "./Pagination";
import StatusBadge from "./StatusBadge";
import useOrders from "../../hooks/order/useOrders";

const OrdersTable = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const { data: orders, isLoading, isError } = useOrders(1);

  if (isLoading) return <p>Loading...</p>;
  if (isError) return <p>Error</p>;
  if (!orders) return <p>No orders found</p>;

  const totalPages = Math.ceil(orders.length / itemsPerPage);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const getCurrentPageData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return orders.slice(startIndex, endIndex);
  };

  return (
    <div className="w-full mx-auto">
      <h1 className="text-2xl font-bold mb-2 text-primary dark:text-white">
        Ordres
      </h1>
      <div className="p-6 rounded-3xl shadow-md bg-base-100 dark:bg-base-300">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-base-300">
                <TableHeader>N° Dossier</TableHeader>
                <TableHeader>Type</TableHeader>
                <TableHeader>Nom</TableHeader>
                <TableHeader>Service</TableHeader>
                <TableHeader>Date</TableHeader>
                <TableHeader>Situation</TableHeader>
                <TableHeader>Prix</TableHeader>
              </tr>
            </thead>
            <tbody className="divide-y divide-base-300">
              {getCurrentPageData().map((order, index) => (
                <tr
                  key={index}
                  className="hover:bg-base-200 transition-colors duration-200"
                >
                  <td className="p-4 text-base-content">
                    {order.numero_dossier}
                  </td>
                  <td className="p-4 text-base-content">
                    {order.service_type}
                  </td>
                  <td className="p-4 text-base-content">
                    {order.service_name}
                  </td>
                  <td className="p-4 text-base-content">
                    {order.service}
                  </td>
                  <td className="p-4 text-base-content">
                    {order.date_creation}
                  </td>
                  <td className="p-4">
                    <StatusBadge status={order.situation} />
                  </td>
                  <td className="p-4 text-base-content">{order.prix}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      </div>
    </div>
  );
};

export default OrdersTable;
