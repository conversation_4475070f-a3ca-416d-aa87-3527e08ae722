import { createBrowserRouter } from "react-router-dom";
import DashboardCoursierPage from "./pages/Coursier/DashboardCoursierPage";
import CommandePage from "./pages/Coursier/CommandePage";
import SignUp from "./pages/LandingPage/SignUp";
import SignIn from "./pages/LandingPage/SignIn";
import VerticalSignUp from "./pages/LandingPage/VerticalSignUp";
import { HomePageLandingPage } from "./pages/LandingPage/HomePageLandingPage";
import DashboardClientPage from "./pages/Client/DashboardClientPage";
import { HomePage } from "./pages/HomePage";
import { Layout } from "./pages/Layout";
import { DashboardEntreprisePage } from "./pages/Entreprise/DashboardEntreprisePage";
import { LandingPageLayout } from "./pages/LandingPage/LandingPageLayout";
import DashboardHomePage from "./pages/Coursier/DashboardHomePage";
import TestPage from "./pages/TestPage";
import AdminRoute from "./components/AdminRoute";

// Enterprise Pages are now directly imported in DashboardEntreprisePage

// Client Pages are now directly imported in DashboardClientPage

// Coursier Pages are now directly imported in DashboardCoursierPage

// Entreprise Pages are now directly imported in DashboardEntreprisePage

// Admin Pages are now directly imported in DashboardAdminPage
import DashboardAdminPage from "./pages/Admin/DashboardAdminPage";


const router = createBrowserRouter([
  {
    path: "/test",
    element: <TestPage />
  },
  {
    path: "/",
    element: <Layout />,

    children: [

      {
        path:"/", element: <LandingPageLayout />,
        children: [

          {path: "/", element: <HomePageLandingPage />},
          {path: "signin", element: <SignIn />},
          {path: "signup", element: <SignUp />},
          {path: "vertical-signup", element: <VerticalSignUp />},
        ],
      },

      {
        path: "coursier",
        element: <DashboardCoursierPage />,
        children: [
          { path: "dashboard", element: <DashboardHomePage /> },
        ],
      },

      // Note: Coursier pages are now directly integrated in DashboardCoursierPage
      // We keep only the detail page that needs a parameter
      { path: "coursier/commandes/:id", element: <CommandePage /> },

      // Client routes
      {
        path: "client",
        element: <DashboardClientPage />,
        children: [
          {path: "dashboard", element: <HomePage />},
        ]
      },

      // Note: Client pages are now directly integrated in DashboardClientPage

      // Client entreprise
      {
        path: "entreprise",
        element: <DashboardEntreprisePage />,
        children: [
          {path: "dashboard", element: <HomePage />},
        ]
      },

      // Note: Enterprise pages are now directly integrated in DashboardEntreprisePage

      // Admin routes
      {
        path: "admin",
        element: <AdminRoute><DashboardAdminPage /></AdminRoute>,
        children: [
          {path: "dashboard", element: <HomePage />},
        ]
      },

      // Note: Admin pages are now directly integrated in DashboardAdminPage
    ],
  }
]);

export default router;
