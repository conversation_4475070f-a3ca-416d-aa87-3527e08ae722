

export interface Solde{
        icon: string,
        title:string,
        price: number,
}

export const SoldeCard= (solde: Solde)=>{

        return (

                <div className="card bg-base-100 w-96 shadow-xl">
                <div className="card-body">
                <h2 className="card-title">{solde.title}</h2>
                <p>{solde.price}</p>
                </div>
                <figure>
                <img
                src={solde.icon}
                alt="Solde" />
                </figure>
                </div>


        );
}