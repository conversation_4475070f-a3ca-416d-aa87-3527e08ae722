import { Link } from 'react-router-dom';
import '/src/index.css';

const FirstSection = () => {
    return (
        <div className="flex flex-col py-10 bg-[#ECF7FF] w-full items-center justify-center relative"> {/* Set relative positioning here */}
            <img className="overlay-image hidden-mobile" src="/src/assets/backgroundSection.svg" />

            {/* Main Container for Content */}
            <div className="flex z-[1] flex-col max-w-full md:flex-row items-start px-4 md:px-20 pt-10">
                {/* Text Content */}
                <div className="flex flex-col items-center md:items-start w-full">
                    <i className="text-sm leading-none text-zinc-400">
                        Neque porro quisquam est qui dolorem ipsum quia
                    </i>
                    <h1 className="mt-6 text-6xl font-bold tracking-tighter text-sky-900 leading-[60px] max-md:max-w-full max-md:text-4xl max-md:leading-10 text-center md:text-left">
                        Simplify your plans with <PERSON><PERSON><PERSON>
                    </h1>
                    <p className="self-stretch mt-11 text-base font-medium tracking-tight leading-7 text-stone-900 max-md:mt-10 max-md:max-w-full text-center md:text-left">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation.
                    </p>
                    <Link className='btn bg-[#184970] text-white mt-8' to={'/signup'}>Voir plus</Link>
                </div>

                {/* Image Section */}
                <div className="flex justify-center md:justify-start w-full mt-6 md:mt-0">
                    <img
                        loading="lazy"
                        src="https://cdn.builder.io/api/v1/image/assets/TEMP/d8cb963f9db73f955a70e1c695b7a1570ef9484cf74e085ea9992d63968b637c?placeholderIfAbsent=true&apiKey=b7978f95452b450fbd6d95e74f4b2ae2"
                        alt="Hero illustration"
                        className="object-contain z-10 max-w-xs md:max-w-none"
                    />
                </div>
            </div>

            {/* Additional Information */}
            <div className="flex z-[1] flex-col self-center -mt-3.5 max-w-full text-2xl font-bold tracking-tight leading-10 text-center text-slate-800 w-[885px]">
                <p className="self-center w-[709px] max-md:max-w-full">
                    De nombreuses entreprises collaborent avec nous pour construire un chemin de réussite pour vous
                </p>
                
            </div>

            {/* Partner Logos Image at the Bottom */}
            <div className='z-[1] w-[60%] mt-5'>
            <img
                loading="lazy"
                src="https://cdn.builder.io/api/v1/image/assets/TEMP/37338d4e038400f6232d8f0a86c5eb31f24cae699d70c28142bc6bd7688f9c54?placeholderIfAbsent=true&apiKey=b7978f95452b450fbd6d95e74f4b2ae2"
                alt="Partner logos"
                className="object-contain z-[1] w-full aspect-[13.33] max-md:mt-10 max-md:max-w-full"
            />
            </div>

        </div>
    );
};

export default FirstSection;

