
interface Props {
  title: string;
  description: string;
  image: string;
  titleColor: string;
  buttonColor: string;
  onClick?: () => void;
}

const ServiceCard = ({
  title,
  description,
  image,
  titleColor,
  buttonColor,
  onClick,
}: Props) => (
  <div className="rounded-3xl shadow-md p-6 flex flex-col items-center">
    <h3 className={`text-lg font-semibold mb-2 ${titleColor}`}>{title}</h3>
    <p className="text-sm text-gray-600 mb-2">Text area 1234</p>
    <p className="text-xs text-gray-500 mb-4">{description}</p>
    <img src={image} alt={title} className="w-24 h-24 mb-4 rounded-full" />
    <button
      onClick={onClick}
      className={`w-full py-2 rounded-3xl text-white font-semibold ${buttonColor}`}
    >
      Créer
    </button>
  </div>
);


export default ServiceCard;