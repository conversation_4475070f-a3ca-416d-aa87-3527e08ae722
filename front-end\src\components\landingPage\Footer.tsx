import React from 'react';
import { FaTwitter, FaFacebook, FaYoutube, FaInstagram, FaWhatsapp } from 'react-icons/fa';

interface FooterProps {
  // Vous pouvez ajouter des props personnalisées ici si nécessaire
}

const Footer: React.FC<FooterProps> = () => {
  return (
    <footer className="bg-white border-t py-8 px-4 mt-10">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Colonne 1 - À propos */}
          <div>
            <div className="mb-4">
              <img src="src/assets/logo.svg" alt="charikti.ma Logo" className="h-11" />
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Un moyen très simple de faire vos achats en toute simplicité.
            </p>
            <div>
              <p className="text-gray-500 font-semibold text-sm mb-2">RETROUVEZ-NOUS SUR :</p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-500 hover:text-gray-700">
                  <FaTwitter size={18} />
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700">
                  <FaFacebook size={18} />
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700">
                  <FaYoutube size={18} />
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700">
                  <FaInstagram size={18} />
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700">
                  <FaWhatsapp size={18} />
                </a>
              </div>
            </div>
          </div>

          {/* Colonne 2 - charikti.ma */}
          <div>
            <h3 className="text-[#5b99c2] font-medium mb-4">Nos Programme</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Qui sommes nous ?
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Carrières
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Boutiques officielles
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Informations légales
                </a>
              </li>
            </ul>
          </div>

          {/* Colonne 3 - Service clients */}
          <div>
            <h3 className="text-[#5b99c2] font-medium mb-4">Nos Services</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Contactez-nous
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Centre d'assistance
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Vendez sur charikti.ma
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Modes de paiement
                </a>
              </li>
            </ul>
          </div>

          {/* Colonne 4 - Retour & Remboursement */}
          <div>
            <h3 className="text-[#5b99c2] font-medium mb-4">Licenece </h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Retour & échange
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Expédition et livraison
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Meilleurs prix garantis
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-gray-800 text-sm">
                  Suivi de votre commande
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;