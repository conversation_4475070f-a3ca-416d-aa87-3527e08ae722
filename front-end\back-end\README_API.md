# API Documentation

This document provides information about the API endpoints available in the application.

## Base URL

```
http://localhost:8000/api
```

## Authentication

The API uses Laravel Sanctum for authentication. You need to include the authentication token in the header of your requests:

```
Authorization: Bearer YOUR_TOKEN
```

### Register

Register a new user.

- **URL**: `/register`
- **Method**: `POST`
- **Auth required**: No
- **Data constraints**:

```json
{
  "name": "[string]",
  "email": "[valid email]",
  "password": "[string min 8 chars]",
  "password_confirmation": "[same as password]",
  "role": "[client|coursier|entreprise]"
}
```

- **Success Response**: `201 Created`

```json
{
  "user": {
    "id": 1,
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "role": "client",
    "created_at": "2023-10-01T12:00:00.000000Z",
    "updated_at": "2023-10-01T12:00:00.000000Z"
  },
  "token": "YOUR_AUTH_TOKEN",
  "token_type": "Bearer"
}
```

### Login

Login a user and get an authentication token.

- **URL**: `/login`
- **Method**: `POST`
- **Auth required**: No
- **Data constraints**:

```json
{
  "email": "[valid email]",
  "password": "[string]"
}
```

- **Success Response**: `200 OK`

```json
{
  "user": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "client",
    "created_at": "2023-10-01T12:00:00.000000Z",
    "updated_at": "2023-10-01T12:00:00.000000Z"
  },
  "token": "YOUR_AUTH_TOKEN",
  "token_type": "Bearer"
}
```

### Get User

Get the authenticated user's information.

- **URL**: `/user`
- **Method**: `GET`
- **Auth required**: Yes
- **Success Response**: `200 OK`

```json
{
  "id": 1,
  "name": "John Doe",
  "email": "<EMAIL>",
  "role": "client",
  "created_at": "2023-10-01T12:00:00.000000Z",
  "updated_at": "2023-10-01T12:00:00.000000Z"
}
```

### Logout

Logout the user (revoke the token).

- **URL**: `/logout`
- **Method**: `POST`
- **Auth required**: Yes
- **Success Response**: `200 OK`

```json
{
  "message": "Successfully logged out"
}
```

### Change Password

Change the user's password.

- **URL**: `/change-password`
- **Method**: `POST`
- **Auth required**: Yes
- **Data constraints**:

```json
{
  "current_password": "[string]",
  "new_password": "[string min 8 chars]",
  "new_password_confirmation": "[same as new_password]"
}
```

- **Success Response**: `200 OK`

```json
{
  "message": "Password changed successfully"
}
```

## Client Endpoints

### Get All Clients

Get a list of all clients.

- **URL**: `/clients`
- **Method**: `GET`
- **Auth required**: Yes
- **Success Response**: `200 OK`

```json
[
  {
    "id": 1,
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone_number": "+212612345678",
    "address": "Casablanca, Morocco",
    "company_name": "ABC Company",
    "profile_image": "profile_images/clients/image.jpg",
    "user_id": 1,
    "created_at": "2023-10-01T12:00:00.000000Z",
    "updated_at": "2023-10-01T12:00:00.000000Z"
  }
]
```

### Get Client Profile

Get the authenticated client's profile.

- **URL**: `/clients/profile`
- **Method**: `GET`
- **Auth required**: Yes
- **Success Response**: `200 OK`

```json
{
  "id": 1,
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone_number": "+212612345678",
  "address": "Casablanca, Morocco",
  "company_name": "ABC Company",
  "profile_image": "profile_images/clients/image.jpg",
  "user_id": 1,
  "created_at": "2023-10-01T12:00:00.000000Z",
  "updated_at": "2023-10-01T12:00:00.000000Z"
}
```

### Get Client by ID

Get a specific client by ID.

- **URL**: `/clients/{id}`
- **Method**: `GET`
- **Auth required**: Yes
- **Success Response**: `200 OK`

```json
{
  "id": 1,
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone_number": "+212612345678",
  "address": "Casablanca, Morocco",
  "company_name": "ABC Company",
  "profile_image": "profile_images/clients/image.jpg",
  "user_id": 1,
  "created_at": "2023-10-01T12:00:00.000000Z",
  "updated_at": "2023-10-01T12:00:00.000000Z"
}
```

### Create Client

Create a new client profile.

- **URL**: `/clients`
- **Method**: `POST`
- **Auth required**: Yes
- **Data constraints**:

```json
{
  "first_name": "[string]",
  "last_name": "[string]",
  "email": "[valid email]",
  "phone_number": "[string]",
  "address": "[string]",
  "company_name": "[string, optional]",
  "profile_image": "[file, optional]"
}
```

- **Success Response**: `201 Created`

```json
{
  "id": 1,
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone_number": "+212612345678",
  "address": "Casablanca, Morocco",
  "company_name": "ABC Company",
  "profile_image": "profile_images/clients/image.jpg",
  "user_id": 1,
  "created_at": "2023-10-01T12:00:00.000000Z",
  "updated_at": "2023-10-01T12:00:00.000000Z"
}
```

### Update Client

Update a client profile.

- **URL**: `/clients/{id}`
- **Method**: `PUT`
- **Auth required**: Yes
- **Data constraints**:

```json
{
  "first_name": "[string, optional]",
  "last_name": "[string, optional]",
  "email": "[valid email, optional]",
  "phone_number": "[string, optional]",
  "address": "[string, optional]",
  "company_name": "[string, optional]"
}
```

- **Success Response**: `200 OK`

```json
{
  "id": 1,
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone_number": "+212612345678",
  "address": "Casablanca, Morocco",
  "company_name": "ABC Company",
  "profile_image": "profile_images/clients/image.jpg",
  "user_id": 1,
  "created_at": "2023-10-01T12:00:00.000000Z",
  "updated_at": "2023-10-01T12:00:00.000000Z"
}
```

### Update Client Profile Image

Update a client's profile image.

- **URL**: `/clients/{id}/profile-image`
- **Method**: `POST`
- **Auth required**: Yes
- **Data constraints**: Form data with `profile_image` field containing an image file.
- **Success Response**: `200 OK`

```json
{
  "message": "Profile image updated successfully",
  "profile_image": "profile_images/clients/image.jpg"
}
```

### Delete Client

Delete a client profile.

- **URL**: `/clients/{id}`
- **Method**: `DELETE`
- **Auth required**: Yes
- **Success Response**: `200 OK`

```json
{
  "message": "Client deleted successfully"
}
```

## Coursier Endpoints

Similar to client endpoints, but with the following differences:

- Base URL: `/coursiers`
- Data fields:
  - `name`
  - `email`
  - `phone_number`
  - `address`
  - `vehicle_type`
  - `license_number`
  - `profile_image`

## Entreprise Endpoints

Similar to client endpoints, but with the following differences:

- Base URL: `/entreprises`
- Data fields:
  - `denomination`
  - `email`
  - `phone_number`
  - `siege_social`
  - `forme_juridique`
  - `activite`
  - `n_rc`
  - `n_cnss`
  - `i_fiscale`
  - `tax_professionel`
  - `profile_image`
