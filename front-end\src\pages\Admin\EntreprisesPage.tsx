import React, { useState, useEffect } from 'react';
import { MdEdit, Md<PERSON><PERSON><PERSON>, MdAdd, MdSearch } from 'react-icons/md';
import apiService from '../../services/apiService';
import InnerPageContainer from '../../components/InnerPageContainer';
import EntrepriseModal from './modals/EntrepriseModal';

interface Entreprise {
  id: number;
  denomination: string;
  email: string;
  phone_number: string;
  siege_social: string;
  forme_juridique: string;
  activite: string;
  n_rc: string;
  n_cnss: string;
  i_fiscale: string;
  tax_professionel: string;
  ice?: string;
  profile_image?: string;
}

const EntreprisesPage = () => {
  const [entreprises, setEntreprises] = useState<Entreprise[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentEntreprise, setCurrentEntreprise] = useState<Entreprise | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [entrepriseToDelete, setEntrepriseToDelete] = useState<Entreprise | null>(null);

  useEffect(() => {
    fetchEntreprises();
  }, []);

  const fetchEntreprises = async () => {
    try {
      setLoading(true);
      const data = await apiService.entreprise.getAll();
      setEntreprises(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching entreprises:', err);
      setError('Erreur lors du chargement des entreprises');
    } finally {
      setLoading(false);
    }
  };

  const handleAddEntreprise = () => {
    setCurrentEntreprise(null);
    setIsModalOpen(true);
  };

  const handleEditEntreprise = (entreprise: Entreprise) => {
    setCurrentEntreprise(entreprise);
    setIsModalOpen(true);
  };

  const handleDeleteClick = (entreprise: Entreprise) => {
    setEntrepriseToDelete(entreprise);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!entrepriseToDelete) return;

    try {
      await apiService.entreprise.delete(entrepriseToDelete.id);
      setEntreprises(entreprises.filter(e => e.id !== entrepriseToDelete.id));
      setIsDeleteModalOpen(false);
      setEntrepriseToDelete(null);
    } catch (err) {
      console.error('Error deleting entreprise:', err);
      setError('Erreur lors de la suppression de l\'entreprise');
    }
  };

  const handleSaveEntreprise = async (entrepriseData: Partial<Entreprise>) => {
    try {
      if (currentEntreprise) {
        // Update existing entreprise
        const updatedEntreprise = await apiService.entreprise.update(currentEntreprise.id, entrepriseData);
        setEntreprises(entreprises.map(e => e.id === currentEntreprise.id ? updatedEntreprise : e));
      } else {
        // Create new entreprise
        const newEntreprise = await apiService.entreprise.create(entrepriseData as any);
        setEntreprises([...entreprises, newEntreprise]);
      }
      setIsModalOpen(false);
    } catch (err) {
      console.error('Error saving entreprise:', err);
      setError('Erreur lors de l\'enregistrement de l\'entreprise');
    }
  };

  const filteredEntreprises = entreprises.filter(entreprise =>
    entreprise.denomination.toLowerCase().includes(searchTerm.toLowerCase()) ||
    entreprise.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    entreprise.forme_juridique.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <InnerPageContainer>
      <div className="p-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Gestion des Entreprises</h1>
          <button
            onClick={handleAddEntreprise}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <MdAdd className="mr-2" /> Ajouter une entreprise
          </button>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Rechercher une entreprise..."
              className="w-full px-4 py-2 pl-10 border rounded-lg"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <MdSearch className="absolute left-3 top-3 text-gray-400" />
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        ) : (
          <div className="overflow-x-auto bg-white rounded-lg shadow">
            <table className="min-w-full">
              <thead>
                <tr className="bg-gray-50">
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dénomination
                  </th>
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Forme juridique
                  </th>
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Téléphone
                  </th>
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Activité
                  </th>
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredEntreprises.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="py-4 px-4 text-center text-gray-500">
                      Aucune entreprise trouvée
                    </td>
                  </tr>
                ) : (
                  filteredEntreprises.map((entreprise) => (
                    <tr key={entreprise.id} className="hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          {entreprise.profile_image ? (
                            <img
                              src={`http://localhost:8000/storage/${entreprise.profile_image}`}
                              alt={entreprise.denomination}
                              className="h-10 w-10 rounded-full mr-3 object-cover"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                              <span className="text-green-500 font-semibold">
                                {entreprise.denomination.charAt(0)}
                              </span>
                            </div>
                          )}
                          <div>
                            <div className="font-medium text-gray-900">
                              {entreprise.denomination}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">{entreprise.forme_juridique}</td>
                      <td className="py-3 px-4">{entreprise.email}</td>
                      <td className="py-3 px-4">{entreprise.phone_number}</td>
                      <td className="py-3 px-4">{entreprise.activite}</td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEditEntreprise(entreprise)}
                            className="text-blue-500 hover:text-blue-700"
                          >
                            <MdEdit className="text-xl" />
                          </button>
                          <button
                            onClick={() => handleDeleteClick(entreprise)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <MdDelete className="text-xl" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Entreprise Modal */}
      {isModalOpen && (
        <EntrepriseModal
          entreprise={currentEntreprise}
          onClose={() => setIsModalOpen(false)}
          onSave={handleSaveEntreprise}
        />
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h2 className="text-xl font-bold mb-4">Confirmer la suppression</h2>
            <p className="mb-6">
              Êtes-vous sûr de vouloir supprimer l'entreprise{' '}
              <span className="font-semibold">
                {entrepriseToDelete?.denomination}
              </span>
              ? Cette action est irréversible.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 border rounded-lg hover:bg-gray-100"
              >
                Annuler
              </button>
              <button
                onClick={handleDeleteConfirm}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
              >
                Supprimer
              </button>
            </div>
          </div>
        </div>
      )}
    </InnerPageContainer>
  );
};

export default EntreprisesPage;
