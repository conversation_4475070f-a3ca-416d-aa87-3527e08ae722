import React from 'react';
import { 
  <PERSON>, 
  Typo<PERSON>, 
  <PERSON><PERSON>, 
  Card, 
  CardContent,
  Grid,
  Alert
} from '@mui/material';
import { useNavigate } from 'react-router';
import { useAuthContext } from 'src/context/AuthContext';
import PageContainer from 'src/components/container/PageContainer';
import DashboardCard from 'src/components/shared/DashboardCard';

const TestPage = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthContext();

  const testRoutes = [
    { path: '/', label: 'Landing Page', description: 'Page d\'accueil publique' },
    { path: '/auth/login', label: 'Connexion', description: 'Page de connexion' },
    { path: '/auth/register', label: 'Inscription', description: 'Page d\'inscription' },
    { path: '/dashboard', label: 'Redirection Dashboard', description: 'Redirection basée sur le rôle' },
    { path: '/client/dashboard', label: 'Dashboard Client', description: 'Tableau de bord client' },
    { path: '/coursier/dashboard', label: 'Dashboard Coursier', description: 'Tableau de bord coursier' },
    { path: '/entreprise/dashboard', label: 'Dashboard Entreprise', description: 'Tableau de bord entreprise' },
    { path: '/sample/page', label: 'Sample Page', description: 'Page d\'exemple du template' },
  ];

  return (
    <PageContainer title="Page de Test" description="Page de test pour vérifier les routes">
      <DashboardCard title="Test des Routes Charikti">
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Page de Test - Routes Charikti
          </Typography>
          <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
            Cette page permet de tester toutes les routes de l'application Charikti intégrées dans le dashboard Modernize.
          </Typography>
          
          {isAuthenticated ? (
            <Alert severity="success" sx={{ mb: 2 }}>
              Connecté en tant que: <strong>{user?.name}</strong> ({user?.role})
            </Alert>
          ) : (
            <Alert severity="info" sx={{ mb: 2 }}>
              Non connecté - Certaines routes nécessitent une authentification
            </Alert>
          )}
        </Box>

        <Grid container spacing={3}>
          {testRoutes.map((route, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card 
                sx={{ 
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  },
                }}
                onClick={() => navigate(route.path)}
              >
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {route.label}
                  </Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                    {route.description}
                  </Typography>
                  <Typography variant="caption" color="primary">
                    {route.path}
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Button 
                      variant="outlined" 
                      size="small" 
                      fullWidth
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(route.path);
                      }}
                    >
                      Tester
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 4, p: 3, bgcolor: 'background.paper', borderRadius: 2 }}>
          <Typography variant="h6" gutterBottom>
            Informations de Debug
          </Typography>
          <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
            {JSON.stringify({
              isAuthenticated,
              user: user ? {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role
              } : null,
              currentPath: window.location.pathname
            }, null, 2)}
          </Typography>
        </Box>
      </DashboardCard>
    </PageContainer>
  );
};

export default TestPage;
