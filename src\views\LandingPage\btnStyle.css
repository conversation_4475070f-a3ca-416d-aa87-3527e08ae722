/*===== GOOGLE FONTS =====*/
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap");
/*===== VARIABLES CSS =====*/
:root {
    /*===== Colores =====*/
    --first-color: #1A73E8;
    --input-color: #80868B;
    --border-color: #DADCE0;
    /*===== Fuente y tipografia =====*/
    --body-font: "Roboto", sans-serif;
    --normal-font-size: 1rem;
    --small-font-size: .75rem;
}
/*===== BASE =====*/
.l-form {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.form {
    width: 360px;
    padding: 3rem 2rem;
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(92, 99, 105, 0.2);
}
.form__title {
    font-weight: 500;
    margin-bottom: 2.5rem;
}
.form__div {
    position: relative;
    height: 52px;
    margin-bottom: 1.5rem;
}
.form__input {
    width: 100%;
    height: 100%;
    font-size: var(--normal-font-size);
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    outline: none;
    padding: 1rem;
    padding-top: 1.5rem; /* Add space for label */
    background: none;
    z-index: 1;
}
.form__label {
    position: absolute;
    left: 1rem;
    top: 0.5rem; /* Position the label */
    background-color: #fff;
    color: var(--input-color);
    font-size: var(--normal-font-size);
    transition: 0.3s;
    padding: 0 0.25rem; /* To give some space around the label */
}

/*Input focus*/
.form__input:focus {
    border: 2px solid var(--first-color);
}

/* Keep the label at the top */
.form__input:focus + .form__label,
.form__input:not(:placeholder-shown) + .form__label {
    top: -0.5rem; /* Adjust this value for your design */
    left: 0.8rem; /* Adjust for the left position */
    color: var(--first-color);
    font-size: var(--small-font-size);
    font-weight: 500;
    z-index: 10;
}