import {Link} from "react-router-dom";
import {SelectableLink} from "./SelectableLink";
import {useState} from "react";


export const OfferCard = ({ img, title, address, price, id }) => {
    const [isChecked, setIsChecked] = useState(false);

    const handleClick = () => {
        setIsChecked(prev => !prev); // Toggle the checked state
        // You can also pass the ID to your data handler here
        console.log(`Link ID: ${id}`);
    };

    return (
        <div className="card p-3 m-5 bg-white card-bordered shadow-xl w-1/3 h-1/2 ">
            <div className={"flex flex-col md:flex-row "}>
                <img src={img} className="flex w-full pr-3 w-[100px] h-[100px] justify-end" />

                <div>
                    <h2 className="card-title">{title}</h2>
                    <p>{address}</p><br/>
                </div>

            </div>

            <div className="flex-1 p-4 text-left flex flex-col justify-between">
                {/* Centered button at the bottom */}
                <div className="flex justify-center mt-auto">
                    <h3 className={"font-bold mr-auto"}>Price: {price} DH</h3>
                    <Link
                        to="#"
                        onClick={handleClick}
                        className={`btn text-white px-4 py-2 rounded-lg transition-colors duration-300 
                ${isChecked ? 'bg-green-500' : 'bg-gray-500'}`}
                    >
                        Take Offer
                    </Link>                </div>
            </div>


        </div>
    );
}