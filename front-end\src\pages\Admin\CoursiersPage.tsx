import React, { useState, useEffect } from 'react';
import { MdEdit, MdD<PERSON><PERSON>, MdAdd, MdSearch } from 'react-icons/md';
import apiService from '../../services/apiService';
import InnerPageContainer from '../../components/InnerPageContainer';
import CoursierModal from './modals/CoursierModal';

interface Coursier {
  id: number;
  name: string;
  email: string;
  phone_number: string;
  address: string;
  vehicle_type: string;
  license_number: string;
  profile_image?: string;
}

const CoursiersPage = () => {
  const [coursiers, setCoursiers] = useState<Coursier[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentCoursier, setCurrentCoursier] = useState<Coursier | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [coursierToDelete, setCoursierToDelete] = useState<Coursier | null>(null);

  useEffect(() => {
    fetchCoursiers();
  }, []);

  const fetchCoursiers = async () => {
    try {
      setLoading(true);
      const data = await apiService.coursier.getAll();
      setCoursiers(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching coursiers:', err);
      setError('Erreur lors du chargement des coursiers');
    } finally {
      setLoading(false);
    }
  };

  const handleAddCoursier = () => {
    setCurrentCoursier(null);
    setIsModalOpen(true);
  };

  const handleEditCoursier = (coursier: Coursier) => {
    setCurrentCoursier(coursier);
    setIsModalOpen(true);
  };

  const handleDeleteClick = (coursier: Coursier) => {
    setCoursierToDelete(coursier);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!coursierToDelete) return;
    
    try {
      await apiService.coursier.delete(coursierToDelete.id);
      setCoursiers(coursiers.filter(c => c.id !== coursierToDelete.id));
      setIsDeleteModalOpen(false);
      setCoursierToDelete(null);
    } catch (err) {
      console.error('Error deleting coursier:', err);
      setError('Erreur lors de la suppression du coursier');
    }
  };

  const handleSaveCoursier = async (coursierData: Partial<Coursier>) => {
    try {
      if (currentCoursier) {
        // Update existing coursier
        const updatedCoursier = await apiService.coursier.update(currentCoursier.id, coursierData);
        setCoursiers(coursiers.map(c => c.id === currentCoursier.id ? updatedCoursier : c));
      } else {
        // Create new coursier
        const newCoursier = await apiService.coursier.create(coursierData as any);
        setCoursiers([...coursiers, newCoursier]);
      }
      setIsModalOpen(false);
    } catch (err) {
      console.error('Error saving coursier:', err);
      setError('Erreur lors de l\'enregistrement du coursier');
    }
  };

  const filteredCoursiers = coursiers.filter(coursier => 
    coursier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    coursier.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <InnerPageContainer>
      <div className="p-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Gestion des Coursiers</h1>
          <button
            onClick={handleAddCoursier}
            className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <MdAdd className="mr-2" /> Ajouter un coursier
          </button>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Rechercher un coursier..."
              className="w-full px-4 py-2 pl-10 border rounded-lg"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <MdSearch className="absolute left-3 top-3 text-gray-400" />
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        ) : (
          <div className="overflow-x-auto bg-white rounded-lg shadow">
            <table className="min-w-full">
              <thead>
                <tr className="bg-gray-50">
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nom
                  </th>
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Téléphone
                  </th>
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Adresse
                  </th>
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type de véhicule
                  </th>
                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredCoursiers.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="py-4 px-4 text-center text-gray-500">
                      Aucun coursier trouvé
                    </td>
                  </tr>
                ) : (
                  filteredCoursiers.map((coursier) => (
                    <tr key={coursier.id} className="hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          {coursier.profile_image ? (
                            <img
                              src={`http://localhost:8000/storage/${coursier.profile_image}`}
                              alt={coursier.name}
                              className="h-10 w-10 rounded-full mr-3 object-cover"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                              <span className="text-yellow-500 font-semibold">
                                {coursier.name.charAt(0)}
                              </span>
                            </div>
                          )}
                          <div>
                            <div className="font-medium text-gray-900">
                              {coursier.name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">{coursier.email}</td>
                      <td className="py-3 px-4">{coursier.phone_number}</td>
                      <td className="py-3 px-4">{coursier.address}</td>
                      <td className="py-3 px-4">{coursier.vehicle_type}</td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEditCoursier(coursier)}
                            className="text-blue-500 hover:text-blue-700"
                          >
                            <MdEdit className="text-xl" />
                          </button>
                          <button
                            onClick={() => handleDeleteClick(coursier)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <MdDelete className="text-xl" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Coursier Modal */}
      {isModalOpen && (
        <CoursierModal
          coursier={currentCoursier}
          onClose={() => setIsModalOpen(false)}
          onSave={handleSaveCoursier}
        />
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h2 className="text-xl font-bold mb-4">Confirmer la suppression</h2>
            <p className="mb-6">
              Êtes-vous sûr de vouloir supprimer le coursier{' '}
              <span className="font-semibold">
                {coursierToDelete?.name}
              </span>
              ? Cette action est irréversible.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 border rounded-lg hover:bg-gray-100"
              >
                Annuler
              </button>
              <button
                onClick={handleDeleteConfirm}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
              >
                Supprimer
              </button>
            </div>
          </div>
        </div>
      )}
    </InnerPageContainer>
  );
};

export default CoursiersPage;
