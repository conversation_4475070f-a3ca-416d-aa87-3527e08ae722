import React from 'react';
import { 
  Box, 
  Container, 
  Typo<PERSON>, 
  But<PERSON>, 
  <PERSON>rid, 
  Card, 
  CardContent,
  useTheme,
  alpha
} from '@mui/material';
import { 
  IconTruck, 
  IconBuildingStore, 
  IconUser, 
  IconShield,
  IconClock,
  IconMapPin
} from '@tabler/icons-react';
import { useNavigate } from 'react-router';
import PageContainer from 'src/components/container/PageContainer';

const LandingPage = () => {
  const theme = useTheme();
  const navigate = useNavigate();

  const services = [
    {
      icon: IconTruck,
      title: 'Livraison Express',
      description: 'Livraison rapide et sécurisée dans toute la ville',
      color: theme.palette.primary.main,
    },
    {
      icon: IconBuildingStore,
      title: 'Services Entreprise',
      description: 'Solutions complètes pour les entreprises',
      color: theme.palette.secondary.main,
    },
    {
      icon: IconShield,
      title: 'Sécurisé',
      description: 'Vos colis sont assurés et suivis en temps réel',
      color: theme.palette.success.main,
    },
  ];

  const features = [
    {
      icon: IconClock,
      title: 'Disponible 24/7',
      description: 'Service disponible à toute heure',
    },
    {
      icon: IconMapPin,
      title: 'Suivi en temps réel',
      description: 'Suivez vos colis en direct',
    },
    {
      icon: IconUser,
      title: 'Support client',
      description: 'Équipe dédiée à votre service',
    },
  ];

  return (
    <PageContainer title="Charikti - Plateforme de livraison" description="Plateforme de livraison moderne">
      {/* Hero Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          color: 'white',
          py: 8,
          mb: 6,
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography variant="h2" component="h1" gutterBottom fontWeight="bold">
                Charikti
              </Typography>
              <Typography variant="h5" component="h2" gutterBottom sx={{ opacity: 0.9 }}>
                Votre partenaire de confiance pour la livraison
              </Typography>
              <Typography variant="body1" sx={{ mb: 4, opacity: 0.8 }}>
                Plateforme moderne de livraison connectant clients, coursiers et entreprises 
                pour un service rapide et fiable.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  size="large"
                  sx={{
                    bgcolor: 'white',
                    color: theme.palette.primary.main,
                    '&:hover': {
                      bgcolor: alpha(theme.palette.common.white, 0.9),
                    },
                  }}
                  onClick={() => navigate('/auth/login')}
                >
                  Se connecter
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  sx={{
                    borderColor: 'white',
                    color: 'white',
                    '&:hover': {
                      borderColor: 'white',
                      bgcolor: alpha(theme.palette.common.white, 0.1),
                    },
                  }}
                  onClick={() => navigate('/auth/register')}
                >
                  S'inscrire
                </Button>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 300,
                }}
              >
                <IconTruck size={200} stroke={1} color="white" />
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Services Section */}
      <Container maxWidth="lg" sx={{ mb: 8 }}>
        <Typography variant="h3" component="h2" textAlign="center" gutterBottom>
          Nos Services
        </Typography>
        <Typography variant="body1" textAlign="center" sx={{ mb: 6, opacity: 0.7 }}>
          Découvrez nos solutions adaptées à vos besoins
        </Typography>
        
        <Grid container spacing={4}>
          {services.map((service, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  transition: 'transform 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                  },
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Box
                    sx={{
                      display: 'inline-flex',
                      p: 2,
                      borderRadius: '50%',
                      bgcolor: alpha(service.color, 0.1),
                      mb: 3,
                    }}
                  >
                    <service.icon size={40} color={service.color} />
                  </Box>
                  <Typography variant="h5" component="h3" gutterBottom>
                    {service.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {service.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Features Section */}
      <Box sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05), py: 8 }}>
        <Container maxWidth="lg">
          <Typography variant="h3" component="h2" textAlign="center" gutterBottom>
            Pourquoi nous choisir ?
          </Typography>
          
          <Grid container spacing={4} sx={{ mt: 4 }}>
            {features.map((feature, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Box sx={{ textAlign: 'center' }}>
                  <Box
                    sx={{
                      display: 'inline-flex',
                      p: 2,
                      borderRadius: '50%',
                      bgcolor: theme.palette.primary.main,
                      mb: 2,
                    }}
                  >
                    <feature.icon size={32} color="white" />
                  </Box>
                  <Typography variant="h6" component="h3" gutterBottom>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* CTA Section */}
      <Container maxWidth="lg" sx={{ py: 8, textAlign: 'center' }}>
        <Typography variant="h3" component="h2" gutterBottom>
          Prêt à commencer ?
        </Typography>
        <Typography variant="body1" sx={{ mb: 4, opacity: 0.7 }}>
          Rejoignez notre plateforme et découvrez un nouveau niveau de service
        </Typography>
        <Button
          variant="contained"
          size="large"
          onClick={() => navigate('/auth/register')}
        >
          Commencer maintenant
        </Button>
      </Container>
    </PageContainer>
  );
};

export default LandingPage;
