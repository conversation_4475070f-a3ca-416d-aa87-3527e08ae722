import domicialisation from "../../assets/client/domicialisation.png";
import juridique from "../../assets/client/juridique.png";
import accompagnement from "../../assets/client/accompagnement.png";

import ServiceCard from "./ServiceCard";

const SERVICES = [
  {
    title: "Domicialisation",
    description: "XXXXXXXXXXXXXX XXXXXXXXXXXXXX",
    image: domicialisation,
    titleColor: "text-red-400 dark:text-red-400",
    buttonColor:
      "bg-red-400 hover:bg-red-500 dark:bg-red-400 dark:hover:bg-red-500",
    link: "/client/domiciliation"
  },
  {
    title: "Juridique Entreprise",
    description: "XXXXXXXXXXXXXX XXXXXXXXXXXXXX",
    image: juridique,
    titleColor: "text-blue-500 dark:text-blue-500",
    buttonColor:
      "bg-blue-500 hover:bg-blue-600 dark:bg-blue-500 dark:hover:bg-blue-600",
    link: "/client/juridique"
  },
  {
    title: "Accompagnement",
    description: "XXXXXXXXXXXXXX XXXXXXXXXXXXXX",
    image: accompagnement,
    titleColor: "text-blue-400 dark:text-blue-400",
    buttonColor:
      "bg-blue-400 hover:bg-blue-500 dark:bg-blue-400 dark:hover:bg-blue-500",
    link: "/client/accompagnement"
  },
];

interface NotreServiceProps {
  onServiceClick?: (link: string) => void;
}

const NotreService = ({ onServiceClick }: NotreServiceProps) => {
  const handleServiceClick = (link: string) => {
    if (onServiceClick) {
      onServiceClick(link);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h2 className="text-2xl font-bold mb-6 text-primary dark:text-primary">
        Notre Service
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {SERVICES.map((service, index) => (
          <ServiceCard
            key={index}
            {...service}
            onClick={() => handleServiceClick(service.link)}
          />
        ))}
      </div>
    </div>
  );
};

export default NotreService;
