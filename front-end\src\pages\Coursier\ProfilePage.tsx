import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaCheckCircle, Fa<PERSON>ey, FaCar } from 'react-icons/fa';
import { coursierProfileService, CoursierProfile } from '../../services/profileService';
import { useAuthContext } from '../../contexts/AuthContext';
import InnerPageContainer from '../../components/InnerPageContainer';

const ProfilePage = () => {
  const [profileData, setProfileData] = useState<CoursierProfile>({
    name: '',
    email: '',
    phoneNumber: '',
    address: '',
    vehicleType: '',
    licenseNumber: '',
  });

  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { updateProfileImage } = useAuthContext();

  // Charger les données du profil au chargement du composant
  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        setLoading(true);

        // Récupérer les données du profil
        const data = await coursierProfileService.getProfile();
        setProfileData(data);

        // Vérifier s'il y a une image stockée dans le localStorage
        const storedImage = localStorage.getItem('coursier_profile_image');
        if (storedImage && storedImage.startsWith('data:image')) {
          console.log('Image récupérée depuis localStorage');
          setProfileImage(storedImage);
        } else if (data.profileImage) {
          console.log('Image récupérée depuis les données du profil');
          setProfileImage(data.profileImage);
        }

        setError(null);
      } catch (err) {
        setError('Erreur lors du chargement des données du profil');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Réinitialiser les états
      setError(null);
      setSuccess(null);
      setSaving(true);

      // Utiliser FileReader pour convertir l'image en base64
      const reader = new FileReader();

      reader.onload = (event) => {
        if (event.target && event.target.result) {
          // Obtenir l'image en base64
          const base64Image = event.target.result as string;

          // Afficher l'image immédiatement
          setProfileImage(base64Image);

          // Stocker l'image dans localStorage
          localStorage.setItem('coursier_profile_image', base64Image);

          // Déclencher un événement de stockage pour informer les autres composants
          try {
            // Créer un événement de stockage personnalisé
            const storageEvent = new StorageEvent('storage', {
              key: 'coursier_profile_image',
              newValue: base64Image,
              oldValue: localStorage.getItem('coursier_profile_image'),
              storageArea: localStorage
            });

            // Déclencher l'événement
            window.dispatchEvent(storageEvent);
          } catch (error) {
            console.error('Erreur lors de la création de l\'événement de stockage:', error);
          }

          // Mettre à jour les données du profil
          setProfileData(prev => ({
            ...prev,
            profileImage: base64Image
          }));

          // Mettre à jour l'image dans le contexte d'authentification
          updateProfileImage(base64Image);

          // Afficher un message de succès
          setSuccess('Image de profil mise à jour avec succès !');

          // Terminer le chargement
          setSaving(false);
        }
      };

      reader.onerror = () => {
        setError('Erreur lors de la lecture du fichier');
        setSaving(false);
      };

      // Lire le fichier en tant que Data URL (base64)
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSaving(true);
      setSuccess(null);
      setError(null);
      await coursierProfileService.updateProfile(profileData);
      setSuccess('Profil mis à jour avec succès !');

      // Faire défiler vers le haut pour voir le message de succès
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } catch (err) {
      setError('Erreur lors de la mise à jour du profil');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  return (
    <InnerPageContainer title="Profil Coursier">
      {/* Afficher un message de succès s'il y en a un */}
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
          <FaCheckCircle className="mr-2" />
          <p>{success}</p>
        </div>
      )}

      {/* Afficher un message d'erreur s'il y en a un */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {/* Afficher un indicateur de chargement pendant le chargement des données */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <FaSpinner className="animate-spin text-blue-600 text-4xl" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Colonne de gauche - Photo de profil */}
          <div className="md:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Photo de profil</h2>

              <div className="flex flex-col items-center">
                <div className="relative mb-4">
                  <div className="w-40 h-40 rounded-full overflow-hidden border-4 border-gray-200">
                    {profileImage ? (
                      <img
                        src={profileImage}
                        alt="Profile"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          console.error('Erreur de chargement de l\'image:', e);
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-300 flex items-center justify-center text-gray-500">
                        <span className="text-4xl font-bold">
                          {profileData.name ? profileData.name.charAt(0).toUpperCase() : 'C'}
                        </span>
                      </div>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full shadow-md hover:bg-blue-700 transition-colors"
                  >
                    <FaCamera />
                  </button>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleImageChange}
                    accept="image/*"
                    className="hidden"
                  />
                </div>

                <div className="text-center">
                  <h3 className="text-lg font-semibold">
                    {profileData.name}
                  </h3>
                  <p className="text-gray-600">{profileData.email}</p>
                  <div className="mt-2 inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">
                    Coursier
                  </div>
                </div>
              </div>

              {/* Informations supplémentaires */}
              <div className="mt-6">
                <div className="border-t pt-4">
                  {profileData.vehicleType && (
                    <p className="text-sm text-gray-600 mb-1">
                      <span className="font-semibold">Véhicule:</span>{' '}
                      {profileData.vehicleType}
                    </p>
                  )}
                  {profileData.phoneNumber && (
                    <p className="text-sm text-gray-600">
                      <span className="font-semibold">Téléphone:</span>{' '}
                      {profileData.phoneNumber}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Colonne de droite - Informations personnelles et sécurité */}
          <div className="md:col-span-2">
            {/* Informations personnelles */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Informations personnelles</h2>

              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Nom complet
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={profileData.name}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={profileData.email}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                    Numéro de téléphone
                  </label>
                  <input
                    type="tel"
                    id="phoneNumber"
                    name="phoneNumber"
                    value={profileData.phoneNumber}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                    Adresse
                  </label>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    value={profileData.address}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Enregistrement...
                    </>
                  ) : (
                    'Enregistrer les modifications'
                  )}
                </button>
              </form>
            </div>

            {/* Informations du véhicule */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <div className="flex items-center mb-4">
                <FaCar className="text-blue-600 mr-2" />
                <h2 className="text-xl font-semibold">Informations du véhicule</h2>
              </div>

              <form>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label htmlFor="vehicleType" className="block text-sm font-medium text-gray-700 mb-1">
                      Type de véhicule
                    </label>
                    <select
                      id="vehicleType"
                      name="vehicleType"
                      value={profileData.vehicleType}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="Moto">Moto</option>
                      <option value="Voiture">Voiture</option>
                      <option value="Vélo">Vélo</option>
                      <option value="Autre">Autre</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="licenseNumber" className="block text-sm font-medium text-gray-700 mb-1">
                      Numéro de permis
                    </label>
                    <input
                      type="text"
                      id="licenseNumber"
                      name="licenseNumber"
                      value={profileData.licenseNumber}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>
              </form>
            </div>

            {/* Sécurité - Changement de mot de passe */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-4">
                <FaKey className="text-blue-600 mr-2" />
                <h2 className="text-xl font-semibold">Sécurité</h2>
              </div>

              <form>
                <div className="mb-4">
                  <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Mot de passe actuel
                  </label>
                  <input
                    type="password"
                    id="currentPassword"
                    name="currentPassword"
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Nouveau mot de passe
                  </label>
                  <input
                    type="password"
                    id="newPassword"
                    name="newPassword"
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Confirmer le nouveau mot de passe
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
                >
                  Changer le mot de passe
                </button>
              </form>
            </div>
          </div>
        </div>
      )}
    </InnerPageContainer>
  );
};

export default ProfilePage;
