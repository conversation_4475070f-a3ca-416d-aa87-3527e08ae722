import { useQuery } from "@tanstack/react-query";
import { Commande } from "../../models/coursier/commande";
import apiClient from "../../services/api-client";

const COMMANDES_PATH = "/commandes/available";

const useAvailableCommandes = () =>
  useQuery<Commande[], Error>({
    queryKey: ["commandes"],
    queryFn: () =>
      apiClient
        .get<Commande[]>(COMMANDES_PATH)
        .then((response) => response.data),
  });

export default useAvailableCommandes;
