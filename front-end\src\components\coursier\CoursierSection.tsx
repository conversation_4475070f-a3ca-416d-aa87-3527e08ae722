import React from "react";

interface CoursierSectionProps {
  icon: React.ReactNode;
  label: string;
  link: string;
  onLinkClick: (link: string) => void;
}

function CoursierSection({ icon, label, link, onLinkClick }: CoursierSectionProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    onLinkClick(link);
  };

  return (
    <a
      href={link}
      onClick={handleClick}
      className="relative flex items-center px-8 space-x-2 py-4 rounded hover:text-primary group"
    >
      <span className="absolute left-0 top-0 h-full w-1 bg-primary transform scale-y-0 group-hover:scale-y-100 transition-transform duration-300"></span>
      <div className="hover:text-primary">{icon}</div>
      <div className="hover:text-primary text-lg">{label}</div>
    </a>
  );
}

export default CoursierSection;
