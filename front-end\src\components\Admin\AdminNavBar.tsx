import { useState, useRef, useEffect } from "react";
import { <PERSON>a<PERSON><PERSON>ch, Fa<PERSON>ell, Fa<PERSON>ser, FaSignOutAlt, FaChevronDown, FaStar, FaClock } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { useAuthContext } from "../../contexts/AuthContext";
import logo from "../../assets/logo.png";

interface Props {
  title: string;
  userName?: string;
  userImage?: string;
  onProfileClick?: (path: string) => void;
}

const AdminNavBar = ({ title, userName, onProfileClick }: Props) => {
  const navigate = useNavigate();
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const { user, logout } = useAuthContext();

  // Référence pour le menu déroulant
  const menuRef = useRef<HTMLDivElement>(null);

  // Utiliser les informations de l'utilisateur du contexte si disponibles
  const displayName = userName || user?.name || "Admin";

  // Fermer le menu lorsque l'utilisateur clique en dehors
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowProfileMenu(false);
      }
    };

    // Ajouter l'écouteur d'événement lorsque le menu est ouvert
    if (showProfileMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Nettoyer l'écouteur d'événement
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showProfileMenu]);

  const handleLogout = () => {
    // Utiliser la fonction de déconnexion du contexte d'authentification
    logout();

    // Rediriger vers la page d'accueil
    navigate('/');
  };

  return (
    <div className="flex items-center justify-between w-full bg-white shadow-md px-6 py-3">
      {/* Left side - Logo and Search */}
      <div className="flex items-center space-x-6">
        {/* Logo */}
        <div className="flex items-center">
          <span className="text-indigo-600 font-bold text-xl mr-2">MATERIO</span>
        </div>

        {/* Search Bar */}
        <div className="relative hidden md:block">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            placeholder="Rechercher..."
          />
        </div>
      </div>

      {/* Right side - Icons and Profile */}
      <div className="flex items-center space-x-4">
        {/* Stars */}
        <div className="flex items-center text-gray-700">
          <FaStar className="h-4 w-4 text-gray-600 mr-1" />
          <span className="text-sm font-medium">Stars</span>
          <span className="ml-1 text-sm font-semibold bg-gray-200 px-2 py-0.5 rounded">994</span>
        </div>

        {/* Clock/History */}
        <button className="p-2 rounded-full text-gray-600 hover:bg-gray-100">
          <FaClock className="h-5 w-5" />
        </button>

        {/* Notification Button */}
        <button className="p-2 rounded-full text-gray-600 hover:bg-gray-100 relative">
          <FaBell className="h-5 w-5" />
          <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
        </button>

        {/* Profile Dropdown */}
        <div className="relative" ref={menuRef}>
          <button
            className="flex items-center space-x-2 rounded-full focus:outline-none"
            onClick={() => setShowProfileMenu(!showProfileMenu)}
          >
            {/* Profile Image */}
            <div className="h-8 w-8 rounded-full bg-orange-500 flex items-center justify-center text-white font-bold">
              {displayName.charAt(0).toUpperCase()}
            </div>
          </button>

          {/* Menu déroulant */}
          {showProfileMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
              {/* Nom de l'utilisateur en haut du menu */}
              <div className="px-4 py-2 border-b border-gray-200">
                <div className="font-medium text-gray-800">{displayName}</div>
              </div>
              <button
                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                onClick={() => {
                  if (onProfileClick) {
                    onProfileClick('/admin/profile');
                  } else {
                    navigate('/admin/profile');
                  }
                  setShowProfileMenu(false);
                }}
              >
                <FaUser className="mr-2 text-gray-500" /> Mon profil
              </button>
              <button
                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                onClick={handleLogout}
              >
                <FaSignOutAlt className="mr-2 text-gray-500" /> Déconnexion
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminNavBar;
