import React, { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useAuthContext } from 'src/context/AuthContext';
import { Box, CircularProgress, Typography } from '@mui/material';

const RoleBasedRedirect: React.FC = () => {
  const navigate = useNavigate();
  const { user, loading, isAuthenticated } = useAuthContext();

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        // Utilisateur non connecté, rediriger vers la landing page
        navigate('/');
        return;
      }

      // Redirection basée sur le rôle
      switch (user?.role) {
        case 'client':
          navigate('/client/dashboard');
          break;
        case 'coursier':
          navigate('/coursier/dashboard');
          break;
        case 'entreprise':
          navigate('/entreprise/dashboard');
          break;
        case 'admin':
          navigate('/admin/dashboard');
          break;
        default:
          // Rôle non reconnu, rediriger vers la landing page
          navigate('/');
          break;
      }
    }
  }, [user, loading, isAuthenticated, navigate]);

  // Afficher un loader pendant la redirection
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        gap: 2,
      }}
    >
      <CircularProgress size={40} />
      <Typography variant="body1" color="textSecondary">
        Redirection en cours...
      </Typography>
    </Box>
  );
};

export default RoleBasedRedirect;
