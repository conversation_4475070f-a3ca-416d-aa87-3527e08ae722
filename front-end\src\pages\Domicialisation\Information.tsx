import React, { useState } from 'react';
import Steps from "../../components/Steps";
import { RegisterInfo } from "./RegisterInfo";
import { TakeOffer } from "./TakeOffer";
import { Confirmation } from "./Confirmation";
import { information } from "../../models/domicialisation/information";

export const Information = () => {
    const [currentStep, setCurrentStep] = useState(0); // Current step
    const [formData, setFormData] = useState<Partial<information>>({}); // Store the form data

    const steps = ["Information", "Choisir l'offre", "Confirmation"];

    // Handle save data from child component
    const handleSave = (data: Partial<information>) => {
        setFormData((prevData) => ({ ...prevData, ...data })); // Merge new data with existing
    };

    return (
        <div className="m-5 bg-white p-5">
            <Steps steps={steps} currentStep={currentStep} />

            {/* Content for each step */}
            <div>
                {currentStep === 0 && <RegisterInfo onSave={handleSave} formData={formData} />}
                {currentStep === 1 && <TakeOffer />}
                {currentStep === 2 && <Confirmation />}
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-4">
                <button
                    onClick={() => setCurrentStep((prev) => Math.max(prev - 1, 0))}
                    className="btn btn-secondary"
                >
                    Previous
                </button>

                <button
                    onClick={() => {
                        if (currentStep === 0) {
                            // Save form data before moving to the next step
                            handleSave(formData);
                        }
                        setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
                    }}
                    className="btn btn-primary"
                >
                    Next
                </button>
            </div>
        </div>
    );
};
