import { useState } from "react";
import {
  MdDashboard,
  MdPerson,
  MdBusiness,
  MdDeliveryDining,
  MdSettings,
  MdBarChart,
  MdAccountCircle,
} from "react-icons/md";

import NavBar from "../../components/NavBar";
import AdminSidebar from "../../components/Admin/AdminSidebar";
import { useAuthContext } from "../../contexts/AuthContext";
import AdminDashboardHome from "./AdminDashboardHome";

// Import all pages that will be displayed in the dashboard
import AdminProfilePage from "./ProfilePage";
import ClientsPage from "./ClientsPage";
import CoursiersPage from "./CoursiersPage";
import EntreprisesPage from "./EntreprisesPage";
import StatisticsPage from "./StatisticsPage";
import SettingsPage from "./SettingsPage";

// Define the sections of the sidebar
const sections = [
  { icon: MdDashboard, label: "Dashboard", link: "/admin/dashboard" },
  { icon: MdAccountCircle, label: "Profil", link: "/admin/profile" },
  { icon: <PERSON>d<PERSON><PERSON>, label: "Clients", link: "/admin/clients" },
  { icon: MdDeliveryDining, label: "Coursiers", link: "/admin/coursiers" },
  { icon: MdBusiness, label: "Entreprises", link: "/admin/entreprises" },
  { icon: MdBarChart, label: "Statistiques", link: "/admin/statistics" },
  { icon: MdSettings, label: "Paramètres", link: "/admin/settings" },
];

const DashboardAdminPage = () => {
  const [currentPage, setCurrentPage] = useState("/admin/dashboard");
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuthContext();

  const handleLinkClick = (link: string) => {
    if (link !== currentPage) {
      setIsLoading(true);
      setCurrentPage(link);
      // Simuler un temps de chargement court
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    }
  };

  // Fonction pour rendre le composant approprié en fonction de la page actuelle
  const renderCurrentPage = () => {
    switch (currentPage) {
      case "/admin/dashboard":
        return <AdminDashboardHome />;
      case "/admin/profile":
        return <AdminProfilePage />;
      case "/admin/clients":
        return <ClientsPage />;
      case "/admin/coursiers":
        return <CoursiersPage />;
      case "/admin/entreprises":
        return <EntreprisesPage />;
      case "/admin/statistics":
        return <StatisticsPage />;
      case "/admin/settings":
        return <SettingsPage />;
      default:
        return <AdminDashboardHome />;
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <NavBar
        title="Admin Dashboard"
        userName={user?.name}
        userImage={user?.profileImage}
        onProfileClick={handleLinkClick}
      />
      <div className="flex flex-1 overflow-auto">
        {/* Sidebar */}
        <AdminSidebar
          sections={sections}
          onLinkClick={handleLinkClick}
        />

        {/* Main Content */}
        <div className="flex-1 p-4 overflow-auto">
          {isLoading ? (
            <div className="w-full h-full flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="w-full bg-white rounded-lg shadow-md p-4">
              {renderCurrentPage()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardAdminPage;
