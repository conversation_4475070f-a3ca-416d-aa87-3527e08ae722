// Fonction utilitaire pour gérer les erreurs API
export const handleApiError = (error: any, defaultMessage: string): never => {
  console.error('API Error:', error);

  // Extraire un message d'erreur plus spécifique si disponible
  let errorMessage = defaultMessage;
  if (error.response && error.response.data && error.response.data.message) {
    errorMessage = error.response.data.message;
  } else if (error.message) {
    errorMessage = error.message;
  }

  throw new Error(errorMessage);
};
