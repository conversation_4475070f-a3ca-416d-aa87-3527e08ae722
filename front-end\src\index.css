@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles pour le corps et le conteneur racine */
html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden; /* Masquer le défilement horizontal */
}

/* Permettre le défilement vertical sur le corps */
body {
  overflow-y: auto;
}

/* Style pour les barres de défilement dans les navigateurs modernes */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* landing Page Style */
.star-motion {
  position: absolute; /* Position the element absolutely within its nearest positioned ancestor */
  top: 0; /* Align to the top */
  right: 0; /* Align to the left */
  transform: translate(-20%, 10%); /* Center the element */
  width: auto; /* Adjust the size of the image */
  height: auto; /* Maintain aspect ratio */
  z-index: 0; /* Ensure the image is below other elements if needed */

}

.sphere-motion{
  position: absolute; /* Position the element absolutely within its nearest positioned ancestor */
  bottom: 0; /* Align to the top */
  left: 0; /* Align to the left */
  transform: translate(10%, -20%); /* Center the element */
  width: auto; /* Adjust the size of the image */
  height: auto; /* Maintain aspect ratio */
  z-index: 0; /* Ensure the image is below other elements if needed */

}



.overlay-image {
  position: absolute; /* Position the image absolutely */
  bottom: 0; /* Align to the bottom */
  left: 50%; /* Center horizontally */
  transform: translateX(-50%); /* Center the image */
  width: 100%; /* Adjust the size of the image */
  height: auto; /* Maintain aspect ratio */
  z-index: 1; /* Ensure the image is below other content */
}

.manMotion{
  position: absolute;
  bottom: 0;
  right: 0;
  transform: translateY(20%);
  width: 1/2;
  min-height: 425px;
  opacity: 40 ;
  z-index: 2;
}


@media (max-width: 768px) {
  .hidden-mobile {
    display: none; /* Hides the image */
  }
}



/* others */

.theme-switcher-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.5rem;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.theme-switcher-btn:hover {
  color: var(--primary-color);
}


.steps {
  list-style-type: none;
  display: flex;
  justify-content: space-between;
  padding: 0;
  margin: 0;
}

.step {
  flex: 1;
  text-align: center;
  padding: 10px;

}

.step-primary {
  color: black;
  font-weight: bold;
}
