/* Styles pour le nouveau footer */
.footer-container {
  width: 100%;
}

.footer-main {
  background-color: #f0f7f4;
  padding: 1.5rem 0;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-heading {
  color: #5b99c2;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

.footer-link {
  color: #4a5568;
  font-size: 0.75rem;
  line-height: 1.5;
  display: block;
  margin-bottom: 0.5rem;
  transition: color 0.2s ease;
  text-decoration: none;
}

.footer-link:hover {
  color: #5b99c2;
}

.footer-contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.footer-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
  color: #4a5568;
}

.footer-bottom {
  background-color: #5b99c2;
  color: white;
  padding: 0.5rem 0;
  font-size: 0.7rem;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.footer-logo-container {
  margin-bottom: 1rem;
}

.footer-logo {
  max-width: 150px;
  height: auto;
}

.footer-bottom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-bottom-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.footer-bottom-links {
  display: flex;
  gap: 0.5rem;
}

.footer-bottom-link {
  color: white;
  transition: text-decoration 0.2s ease;
  text-decoration: none;
}

.footer-bottom-link:hover {
  text-decoration: underline;
}

.footer-divider {
  margin: 0 0.25rem;
}

.footer-social-icons {
  display: flex;
  gap: 0.75rem;
}

.footer-social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  transition: background-color 0.2s ease;
}

.footer-social-icon:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.footer-social-icon svg {
  width: 16px;
  height: 16px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-bottom-container {
    flex-direction: column;
    gap: 0.75rem;
  }

  .footer-bottom-right {
    flex-direction: column;
    gap: 0.75rem;
  }

  .footer-social-icons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-grid {
    grid-template-columns: 1fr;
  }

  .footer-bottom-links {
    flex-wrap: wrap;
    justify-content: center;
  }
}
