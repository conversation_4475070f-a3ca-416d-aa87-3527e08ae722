<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Coursier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class CoursierController extends Controller
{
    /**
     * Display a listing of the coursiers.
     */
    public function index()
    {
        $coursiers = Coursier::all();
        return response()->json($coursiers);
    }

    /**
     * Store a newly created coursier in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:coursiers',
            'phone_number' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'vehicle_type' => 'required|string|max:50',
            'license_number' => 'required|string|max:50',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'user_id' => 'nullable|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Handle profile image upload
        $profileImagePath = null;
        if ($request->hasFile('profile_image')) {
            $profileImagePath = $request->file('profile_image')->store('profile_images/coursiers', 'public');
        }

        // Si l'utilisateur est authentifié, utiliser son ID, sinon utiliser l'ID fourni dans la requête
        $userId = Auth::id() ?? $request->user_id;

        $coursier = Coursier::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone_number' => $request->phone_number,
            'address' => $request->address,
            'vehicle_type' => $request->vehicle_type,
            'license_number' => $request->license_number,
            'profile_image' => $profileImagePath,
            'user_id' => $userId,
        ]);

        return response()->json($coursier, 201);
    }

    /**
     * Display the specified coursier.
     */
    public function show(Coursier $coursier)
    {
        return response()->json($coursier);
    }

    /**
     * Get the authenticated coursier's profile.
     */
    public function profile()
    {
        $user = Auth::user();
        $coursier = $user->coursier;

        if (!$coursier) {
            return response()->json(['message' => 'Coursier profile not found'], 404);
        }

        return response()->json($coursier);
    }

    /**
     * Update the specified coursier in storage.
     */
    public function update(Request $request, Coursier $coursier)
    {
        // Check if the authenticated user owns this coursier profile
        if (Auth::id() !== $coursier->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|string|email|max:255|unique:coursiers,email,' . $coursier->id,
            'phone_number' => 'sometimes|required|string|max:20',
            'address' => 'sometimes|required|string|max:255',
            'vehicle_type' => 'sometimes|required|string|max:50',
            'license_number' => 'sometimes|required|string|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $coursier->update($request->only([
            'name',
            'email',
            'phone_number',
            'address',
            'vehicle_type',
            'license_number',
        ]));

        return response()->json($coursier);
    }

    /**
     * Update the coursier's profile image.
     */
    public function updateProfileImage(Request $request, Coursier $coursier)
    {
        // Check if the authenticated user owns this coursier profile
        if (Auth::id() !== $coursier->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Delete old profile image if exists
        if ($coursier->profile_image) {
            Storage::disk('public')->delete($coursier->profile_image);
        }

        // Store new profile image
        $profileImagePath = $request->file('profile_image')->store('profile_images/coursiers', 'public');
        $coursier->profile_image = $profileImagePath;
        $coursier->save();

        return response()->json([
            'message' => 'Profile image updated successfully',
            'profile_image' => $profileImagePath
        ]);
    }

    /**
     * Remove the specified coursier from storage.
     */
    public function destroy(Coursier $coursier)
    {
        // Check if the authenticated user owns this coursier profile
        if (Auth::id() !== $coursier->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Delete profile image if exists
        if ($coursier->profile_image) {
            Storage::disk('public')->delete($coursier->profile_image);
        }

        $coursier->delete();

        return response()->json(['message' => 'Coursier deleted successfully']);
    }
}
