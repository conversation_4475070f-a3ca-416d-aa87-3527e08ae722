import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>aEnvelope, FaLock, <PERSON>a<PERSON>ye, FaEyeSlash } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../../contexts/AuthContext';
import { RegisterData } from '../../services/apiService';

// Importation des constantes
const activites: string[] = [
  "Comptable",
  "Expert comptable",
  "Centre d'affaire ",
  "Autres"
];

const formeJuridique: string[] = [
  "SARL",
  "SARL AU",
  "personne physique",
  "Auto entrepreneur ",
  "Autre",
];

interface FormData {
  name: string;
  email: string;
  password: string;
  address: string;
  companyName: string;
  phoneNumber: string;
  nCnss: string;
  nRC: string;
  iFiscale: string;
  formeJuridique: string;
  denomination: string;
  activite: string;
  taxProfessionel: string;
  confirmPassword: string;
  siegeSocial: string;
  ice: string; // Changed from societe to ice for clarity
  cin: string;
}

const MultiStepSignUpForm: React.FC = () => {
  const navigate = useNavigate();
  const { register } = useAuthContext();

  const [currentStep, setCurrentStep] = useState(1);
  const [role, setRole] = useState('client'); // Default role
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    password: '',
    address: '',
    companyName: '',
    phoneNumber: '',
    nCnss: '',
    nRC: '',
    iFiscale: '',
    formeJuridique: '',
    denomination: '',
    activite: '',
    taxProfessionel: '',
    confirmPassword: '',
    siegeSocial: '',
    ice: '',
    cin: '',
  });
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    const updatedFormData = { ...formData, [name]: value };
    setFormData(updatedFormData);

    if (name === "password" || name === "confirmPassword") {
      if (updatedFormData.confirmPassword && updatedFormData.password !== updatedFormData.confirmPassword) {
        setError("Passwords do not match");
      } else {
        setError("");
      }
    }
  };

  const handleRoleChange = (selectedRole: string) => {
    setRole(selectedRole);
    setFormData({
      confirmPassword: "",
      activite: "",
      denomination: "",
      formeJuridique: "",
      iFiscale: "",
      ice: "",
      nCnss: "",
      nRC: "",
      siegeSocial: "",
      taxProfessionel: "",
      name: '',
      email: '',
      password: '',
      address: '',
      companyName: '',
      phoneNumber: '',
      cin: ''
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setIsSubmitting(false);
      return;
    }

    // Clear any previous errors
    setError('');

    try {
      console.log('Form submitted:', formData);

      // Préparer les données d'inscription selon le rôle
      const registerData: RegisterData = {
        name: '',
        email: formData.email,
        password: formData.password,
        password_confirmation: formData.confirmPassword,
        role: role === 'societe' ? 'entreprise' : role as 'client' | 'coursier' | 'entreprise' | 'admin'
      };

      // Définir le nom selon le rôle
      if (role === 'client' || role === 'coursier') {
        registerData.name = formData.name;
      } else if (role === 'societe') {
        registerData.name = formData.denomination;
      }

      console.log('Register data:', registerData);

      // Utiliser la fonction register du hook useAuth
      const success = await register(registerData);

      if (!success) {
        console.error('Registration failed');
        setError('Erreur lors de la création du compte. Veuillez vérifier vos informations.');
        setIsSubmitting(false);
        return;
      }

      if (success) {
        // Créer le profil spécifique selon le rôle
        try {
          // Récupérer le token d'authentification
          const token = localStorage.getItem('token');

          if (role === 'client') {
            const response = await fetch('http://localhost:8000/api/clients', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                first_name: formData.name.split(' ')[0] || 'Prénom',
                last_name: formData.name.split(' ').slice(1).join(' ') || 'Nom',
                email: formData.email,
                phone_number: formData.phoneNumber || '**********',
                address: formData.address || 'Adresse non spécifiée',
                company_name: formData.companyName || ''
              })
            });

            const profileData = await response.json();
            console.log('Client profile created:', profileData);
          } else if (role === 'coursier') {
            const response = await fetch('http://localhost:8000/api/coursiers', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                name: formData.name || 'Nom Complet',
                email: formData.email,
                phone_number: formData.phoneNumber || '**********',
                address: formData.address || 'Adresse non spécifiée',
                vehicle_type: 'Non spécifié',
                license_number: 'Non spécifié'
              })
            });

            const profileData = await response.json();
            console.log('Coursier profile created:', profileData);
          } else if (role === 'societe') {
            // Créer d'abord le profil de l'entreprise
            const entrepriseResponse = await fetch('http://localhost:8000/api/entreprises', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                denomination: formData.denomination || 'Nom Entreprise',
                email: formData.email,
                phone_number: formData.phoneNumber || '**********',
                siege_social: formData.siegeSocial || 'Siège social non spécifié',
                forme_juridique: formData.formeJuridique || 'SARL',
                activite: formData.activite || 'Activité non spécifiée',
                n_rc: formData.nRC || 'Non spécifié',
                n_cnss: formData.nCnss || 'Non spécifié',
                i_fiscale: formData.iFiscale || 'Non spécifié',
                tax_professionel: formData.taxProfessionel || 'Non spécifié',
                ice: formData.ice || 'Non spécifié', // Added ICE field
                // Informations du gérant
                gerant_name: formData.name || 'Non spécifié',
                gerant_cin: formData.cin || 'Non spécifié',
                gerant_address: formData.address || 'Non spécifié'
              })
            });

            const response = entrepriseResponse;

            const profileData = await response.json();
            console.log('Entreprise profile created:', profileData);
          }

          console.log('Profile created successfully');
        } catch (profileError) {
          console.error('Error creating profile:', profileError);
          // Continue anyway, we'll handle profile creation later if needed
        }

        setSubmitSuccess(true);

        // Redirection vers le dashboard approprié après un court délai
        setTimeout(() => {
          switch (role) {
            case 'client':
              navigate('/client/dashboard');
              break;
            case 'coursier':
              navigate('/coursier/dashboard');
              break;
            case 'societe':
              navigate('/entreprise/dashboard');
              break;
            case 'admin':
              navigate('/admin/dashboard');
              break;
            default:
              navigate('/client/dashboard');
          }
        }, 1500); // Redirect after showing success message for 1.5 seconds
      } else {
        setError('Erreur lors de la création du compte');
      }
    } catch (error) {
      console.error('Signup error:', error);
      setError('Une erreur est survenue lors de la création du compte');
    } finally {
      setIsSubmitting(false);
    }
  };

  const goToNextStep = () => {
    setCurrentStep(prev => Math.min(prev + 1, 3));
  };

  const goToPreviousStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(prev => !prev);
  };

  const steps = [
    {
      number: 1,
      title: 'Type de compte',
      description: 'Sélectionnez votre identité',
      icon: <FaUser className="text-white" />
    },
    {
      number: 2,
      title: 'Informations personnelles',
      description: 'Complétez vos informations',
      icon: <FaEnvelope className="text-white" />
    },
    {
      number: 3,
      title: 'Identification',
      description: 'Créez vos identifiants',
      icon: <FaLock className="text-white" />
    }
  ];

  // Étape 1: Sélection du type de compte
  const renderAccountTypeStep = () => (
    <div>
      <h2 className="text-2xl font-semibold mb-6">Sélectionnez votre identité</h2>

      <div className="space-y-4 mb-8">
        <div
          className={`p-4 border rounded-lg cursor-pointer transition-all ${
            role === 'client' ? 'border-indigo-600 bg-indigo-50' : 'border-gray-200 hover:border-indigo-300'
          }`}
          onClick={() => handleRoleChange('client')}
        >
          <div className="flex items-center">
            <div className="mr-4">
              <FaUser className="text-indigo-600 text-xl" />
            </div>
            <div>
              <h3 className="font-medium">Client</h3>
              <p className="text-sm text-gray-500">Pour les utilisateurs individuels</p>
            </div>
          </div>
        </div>

        <div
          className={`p-4 border rounded-lg cursor-pointer transition-all ${
            role === 'coursier' ? 'border-indigo-600 bg-indigo-50' : 'border-gray-200 hover:border-indigo-300'
          }`}
          onClick={() => handleRoleChange('coursier')}
        >
          <div className="flex items-center">
            <div className="mr-4">
              <FaUser className="text-indigo-600 text-xl" />
            </div>
            <div>
              <h3 className="font-medium">Coursier</h3>
              <p className="text-sm text-gray-500">Pour les partenaires de livraison</p>
            </div>
          </div>
        </div>

        <div
          className={`p-4 border rounded-lg cursor-pointer transition-all ${
            role === 'societe' ? 'border-indigo-600 bg-indigo-50' : 'border-gray-200 hover:border-indigo-300'
          }`}
          onClick={() => handleRoleChange('societe')}
        >
          <div className="flex items-center">
            <div className="mr-4">
              <FaUser className="text-indigo-600 text-xl" />
            </div>
            <div>
              <h3 className="font-medium">Société</h3>
              <p className="text-sm text-gray-500">Pour les entreprises et organisations</p>
            </div>
          </div>
        </div>

        <div
          className={`p-4 border rounded-lg cursor-pointer transition-all ${
            role === 'admin' ? 'border-indigo-600 bg-indigo-50' : 'border-gray-200 hover:border-indigo-300'
          }`}
          onClick={() => handleRoleChange('admin')}
        >
          <div className="flex items-center">
            <div className="mr-4">
              <FaUser className="text-indigo-600 text-xl" />
            </div>
            <div>
              <h3 className="font-medium">Administrateur</h3>
              <p className="text-sm text-gray-500">Pour les administrateurs du système</p>
            </div>
          </div>
        </div>
      </div>

      <button
        type="button"
        onClick={goToNextStep}
        className="w-full py-2 px-4 rounded-md text-white font-medium bg-indigo-600 hover:bg-indigo-700"
      >
        Continuer
      </button>
    </div>
  );

  // Étape 2: Informations personnelles
  const renderPersonalInfoStep = () => (
    <div>
      <h2 className="text-2xl font-semibold mb-6">Informations personnelles</h2>

      {(role === 'client' || role === 'coursier') && (
        <div className="space-y-4 mb-8">
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Nom complet
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          {role === 'client' && (
            <div className="mb-4">
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                Adresse
              </label>
              <input
                type="text"
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}

          {role === 'coursier' && (
            <>
              <div className="mb-4">
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Téléphone
                </label>
                <input
                  type="tel"
                  id="phoneNumber"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="mb-4">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                  Adresse
                </label>
                <input
                  type="text"
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </>
          )}
        </div>
      )}

      {role === 'societe' && (
        <div>
          {/* Informations du gérant */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-700 border-b pb-2">Informations du gérant</h3>
            <div className="mb-4">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Nom complet
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Téléphone
                </label>
                <input
                  type="tel"
                  id="phoneNumber"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label htmlFor="cin" className="block text-sm font-medium text-gray-700 mb-1">
                  CIN
                </label>
                <input
                  type="text"
                  id="cin"
                  name="cin"
                  value={formData.cin || ''}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>
            <div className="mb-4">
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                Adresse
              </label>
              <input
                type="text"
                id="address"
                name="address"
                value={formData.address || ''}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
          </div>

          {/* Informations de la société */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-700 border-b pb-2">Informations de la société</h3>

            {/* Dénomination - Full Width */}
            <div className="mb-4">
              <label htmlFor="denomination" className="block text-sm font-medium text-gray-700 mb-1">
                Dénomination
              </label>
              <input
                type="text"
                id="denomination"
                name="denomination"
                value={formData.denomination}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            {/* ICE et Activité */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="ice" className="block text-sm font-medium text-gray-700 mb-1">
                  ICE
                </label>
                <input
                  type="text"
                  id="ice"
                  name="ice"
                  value={formData.ice}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label htmlFor="activite" className="block text-sm font-medium text-gray-700 mb-1">
                  Activité
                </label>
                <select
                  id="activite"
                  name="activite"
                  value={formData.activite}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="" disabled>Sélectionner une activité</option>
                  {activites.map((activite) => (
                    <option key={activites.indexOf(activite)} value={activite}>{activite}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Forme Juridique et Taxe Professionnelle */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="formeJuridique" className="block text-sm font-medium text-gray-700 mb-1">
                  Forme Juridique
                </label>
                <select
                  id="formeJuridique"
                  name="formeJuridique"
                  value={formData.formeJuridique}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="" disabled>Sélectionner une forme juridique</option>
                  {formeJuridique.map((forme) => (
                    <option key={formeJuridique.indexOf(forme)} value={forme}>{forme}</option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="taxProfessionel" className="block text-sm font-medium text-gray-700 mb-1">
                  Taxe professionnelle
                </label>
                <input
                  type="text"
                  id="taxProfessionel"
                  name="taxProfessionel"
                  value={formData.taxProfessionel}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            {/* Identifiant Fiscal et Numéro RC */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="iFiscale" className="block text-sm font-medium text-gray-700 mb-1">
                  Identifiant fiscal
                </label>
                <input
                  type="text"
                  id="iFiscale"
                  name="iFiscale"
                  value={formData.iFiscale}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label htmlFor="nRC" className="block text-sm font-medium text-gray-700 mb-1">
                  Numéro RC
                </label>
                <input
                  type="text"
                  id="nRC"
                  name="nRC"
                  value={formData.nRC}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            {/* Numéro CNSS */}
            <div className="mb-4">
              <label htmlFor="nCnss" className="block text-sm font-medium text-gray-700 mb-1">
                Numéro CNSS
              </label>
              <input
                type="text"
                id="nCnss"
                name="nCnss"
                value={formData.nCnss}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Siège Social - Full Width */}
            <div className="mb-4">
              <label htmlFor="siegeSocial" className="block text-sm font-medium text-gray-700 mb-1">
                Siège Social
              </label>
              <input
                type="text"
                id="siegeSocial"
                name="siegeSocial"
                value={formData.siegeSocial}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between mt-8">
        <button
          type="button"
          onClick={goToPreviousStep}
          className="py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          Retour
        </button>

        <button
          type="button"
          onClick={goToNextStep}
          className="py-2 px-4 rounded-md text-white font-medium bg-indigo-600 hover:bg-indigo-700"
        >
          Continuer
        </button>
      </div>
    </div>
  );

  // Étape 3: Informations d'identification (email et mot de passe)
  const renderIdentificationStep = () => {
    return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center mb-4">
        <div className="mr-3 text-blue-500">
          <FaLock size={20} />
        </div>
        <h2 className="text-xl font-semibold">Informations d'identification</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="mb-4">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            required
          />
        </div>

        <div className="mb-4">
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
            Mot de passe
          </label>
          <div className="relative">
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 pr-10"
              required
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
              onClick={togglePasswordVisibility}
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </button>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
            Confirmer le mot de passe
          </label>
          <div className="relative">
            <input
              type={showConfirmPassword ? "text" : "password"}
              id="confirmPassword"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 pr-10"
              required
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
              onClick={toggleConfirmPasswordVisibility}
            >
              {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
            </button>
          </div>
        </div>

        {error && (
          <div className="text-red-500 text-sm mt-2">
            {error}
          </div>
        )}

        {submitSuccess && (
          <div className="text-green-500 text-sm mt-2">
            Compte créé avec succès!
          </div>
        )}

        <div className="flex justify-between mt-8">
          <button
            type="button"
            onClick={goToPreviousStep}
            className="py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Retour
          </button>

          <button
            type="submit"
            disabled={isSubmitting}
            className="py-2 px-4 rounded-md text-white font-medium bg-blue-600 hover:bg-blue-700"
          >
            {isSubmitting ? 'Création en cours...' : 'Créer mon compte'}
          </button>
        </div>
      </form>
    </div>
  );
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return renderAccountTypeStep();
      case 2:
        return renderPersonalInfoStep();
      case 3:
        return renderIdentificationStep();
      default:
        return null;
    }
  };

  return (
    <div className="flex min-h-screen">
      {/* Left sidebar with steps */}
      <div className="w-[550px] bg-[#b5cce3] p-8 border-top-right-radius-[35px]" style={{ borderTopRightRadius: '35px' }}>
        <div className="mb-10">
          <h1 className="text-xl font-semibold text-indigo-500">Inscrivez-vous pour profiter de tous nos services.</h1>
        </div>

        <div className="space-y-8">
          {steps.map((step, index) => (
            <div key={step.number} className="flex items-start">
              <div
                className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-4 ${
                  currentStep >= step.number
                    ? 'bg-indigo-600'
                    : 'bg-gray-300'
                }`}
              >
                {step.icon}
              </div>
              <div>
                <h3 className="font-medium">{step.title}</h3>
                <p className="text-sm text-gray-500">{step.description}</p>
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`ml-4 mt-8 ${
                    currentStep > step.number ? 'bg-indigo-600' : 'bg-gray-300'
                  }`}
                ></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 p-8 bg-white">
        <div className="max-w-md mx-auto">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  );
};

export default MultiStepSignUpForm;
