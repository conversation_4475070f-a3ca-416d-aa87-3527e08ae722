<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Entreprise;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class EntrepriseController extends Controller
{
    /**
     * Display a listing of the entreprises.
     */
    public function index()
    {
        $entreprises = Entreprise::all();
        return response()->json($entreprises);
    }

    /**
     * Store a newly created entreprise in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'denomination' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:entreprises',
            'phone_number' => 'required|string|max:20',
            'siege_social' => 'required|string|max:255',
            'forme_juridique' => 'required|string|max:50',
            'activite' => 'required|string|max:255',
            'n_rc' => 'required|string|max:50',
            'n_cnss' => 'required|string|max:50',
            'i_fiscale' => 'required|string|max:50',
            'tax_professionel' => 'required|string|max:50',
            'ice' => 'nullable|string|max:50',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'user_id' => 'nullable|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Handle profile image upload
        $profileImagePath = null;
        if ($request->hasFile('profile_image')) {
            $profileImagePath = $request->file('profile_image')->store('profile_images/entreprises', 'public');
        }

        // Si l'utilisateur est authentifié, utiliser son ID, sinon utiliser l'ID fourni dans la requête
        $userId = Auth::id() ?? $request->user_id;

        $entreprise = Entreprise::create([
            'denomination' => $request->denomination,
            'email' => $request->email,
            'phone_number' => $request->phone_number,
            'siege_social' => $request->siege_social,
            'forme_juridique' => $request->forme_juridique,
            'activite' => $request->activite,
            'n_rc' => $request->n_rc,
            'n_cnss' => $request->n_cnss,
            'i_fiscale' => $request->i_fiscale,
            'tax_professionel' => $request->tax_professionel,
            'ice' => $request->ice,
            'profile_image' => $profileImagePath,
            'user_id' => $userId,
        ]);

        return response()->json($entreprise, 201);
    }

    /**
     * Display the specified entreprise.
     */
    public function show(Entreprise $entreprise)
    {
        return response()->json($entreprise);
    }

    /**
     * Get the authenticated entreprise's profile.
     */
    public function profile()
    {
        $user = Auth::user();
        $entreprise = $user->entreprise;

        if (!$entreprise) {
            return response()->json(['message' => 'Entreprise profile not found'], 404);
        }

        return response()->json($entreprise);
    }

    /**
     * Update the specified entreprise in storage.
     */
    public function update(Request $request, Entreprise $entreprise)
    {
        // Check if the authenticated user owns this entreprise profile
        if (Auth::id() !== $entreprise->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'denomination' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|string|email|max:255|unique:entreprises,email,' . $entreprise->id,
            'phone_number' => 'sometimes|required|string|max:20',
            'siege_social' => 'sometimes|required|string|max:255',
            'forme_juridique' => 'sometimes|required|string|max:50',
            'activite' => 'sometimes|required|string|max:255',
            'n_rc' => 'sometimes|required|string|max:50',
            'n_cnss' => 'sometimes|required|string|max:50',
            'i_fiscale' => 'sometimes|required|string|max:50',
            'tax_professionel' => 'sometimes|required|string|max:50',
            'ice' => 'sometimes|nullable|string|max:50',
            // Informations du gérant
            'gerant_name' => 'sometimes|nullable|string|max:255',
            'gerant_cin' => 'sometimes|nullable|string|max:50',
            'gerant_address' => 'sometimes|nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $entreprise->update($request->only([
            'denomination',
            'email',
            'phone_number',
            'siege_social',
            'forme_juridique',
            'activite',
            'n_rc',
            'n_cnss',
            'i_fiscale',
            'tax_professionel',
            'ice',
            // Informations du gérant
            'gerant_name',
            'gerant_cin',
            'gerant_address',
        ]));

        return response()->json($entreprise);
    }

    /**
     * Update the entreprise's profile image.
     */
    public function updateProfileImage(Request $request, Entreprise $entreprise)
    {
        // Check if the authenticated user owns this entreprise profile
        if (Auth::id() !== $entreprise->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Delete old profile image if exists
        if ($entreprise->profile_image) {
            Storage::disk('public')->delete($entreprise->profile_image);
        }

        // Store new profile image
        $profileImagePath = $request->file('profile_image')->store('profile_images/entreprises', 'public');
        $entreprise->profile_image = $profileImagePath;
        $entreprise->save();

        return response()->json([
            'message' => 'Profile image updated successfully',
            'profile_image' => $profileImagePath
        ]);
    }

    /**
     * Remove the specified entreprise from storage.
     */
    public function destroy(Entreprise $entreprise)
    {
        // Check if the authenticated user owns this entreprise profile
        if (Auth::id() !== $entreprise->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Delete profile image if exists
        if ($entreprise->profile_image) {
            Storage::disk('public')->delete($entreprise->profile_image);
        }

        $entreprise->delete();

        return response()->json(['message' => 'Entreprise deleted successfully']);
    }
}
