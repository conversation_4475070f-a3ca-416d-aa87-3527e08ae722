import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>ckCircle, <PERSON><PERSON><PERSON><PERSON>, FaUserShield } from 'react-icons/fa';
import { adminProfileService, AdminProfile } from '../../services/profileService';
import { useAuthContext } from '../../contexts/AuthContext';
import InnerPageContainer from '../../components/InnerPageContainer';

const permissionOptions = [
  { value: 'manage_users', label: 'Gestion des utilisateurs' },
  { value: 'manage_content', label: 'Gestion du contenu' },
  { value: 'view_statistics', label: 'Visualisation des statistiques' },
  { value: 'system_settings', label: 'Paramètres système' },
  { value: 'manage_payments', label: 'Gestion des paiements' },
  { value: 'manage_orders', label: 'Gestion des commandes' },
];

const ProfilePage = () => {
  const [profileData, setProfileData] = useState<AdminProfile>({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    role: 'admin',
    permissions: [],
  });

  // États pour le formulaire de profil
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { updateProfileImage } = useAuthContext();

  // États pour le changement de mot de passe
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
  const [changingPassword, setChangingPassword] = useState(false);

  // Charger les données du profil au chargement du composant
  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        setLoading(true);

        // Récupérer les données du profil
        const data = await adminProfileService.getProfile();
        setProfileData(data);

        // Vérifier s'il y a une image stockée dans le localStorage
        const storedImage = localStorage.getItem('admin_profile_image');
        if (storedImage && storedImage.startsWith('data:image')) {
          console.log('Image récupérée depuis localStorage');
          setProfileImage(storedImage);
        } else if (data.profileImage) {
          console.log('Image récupérée depuis les données du profil');
          setProfileImage(data.profileImage);
        }

        setError(null);
      } catch (err) {
        setError('Erreur lors du chargement des données du profil');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, []);

  // Gérer le changement des champs du formulaire
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileData({ ...profileData, [name]: value });
  };

  // Gérer le changement des champs du formulaire de mot de passe
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData({ ...passwordData, [name]: value });
  };

  // Gérer le changement des permissions
  const handlePermissionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target;
    
    if (checked) {
      // Ajouter la permission si elle n'existe pas déjà
      setProfileData({
        ...profileData,
        permissions: [...(profileData.permissions || []), value]
      });
    } else {
      // Supprimer la permission
      setProfileData({
        ...profileData,
        permissions: (profileData.permissions || []).filter(p => p !== value)
      });
    }
  };

  // Gérer le clic sur le bouton de téléchargement d'image
  const handleImageClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Gérer le téléchargement d'une nouvelle image
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const imageUrl = await adminProfileService.uploadProfileImage(file);
      setProfileImage(imageUrl);
      
      // Mettre à jour l'image de profil dans le contexte d'authentification
      if (updateProfileImage) {
        updateProfileImage(imageUrl);
      }
    } catch (err) {
      console.error('Erreur lors du téléchargement de l\'image:', err);
      setError('Erreur lors du téléchargement de l\'image');
    }
  };

  // Soumettre le formulaire de profil
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSaving(true);
      setSuccess(null);
      setError(null);
      
      // Mettre à jour le profil
      await adminProfileService.updateProfile(profileData);
      
      setSuccess('Profil mis à jour avec succès !');

      // Faire défiler vers le haut pour voir le message de succès
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } catch (err) {
      setError('Erreur lors de la mise à jour du profil');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  // Soumettre le formulaire de changement de mot de passe
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation basique
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setPasswordError('Les mots de passe ne correspondent pas');
      return;
    }
    
    if (passwordData.newPassword.length < 8) {
      setPasswordError('Le mot de passe doit contenir au moins 8 caractères');
      return;
    }
    
    try {
      setChangingPassword(true);
      setPasswordSuccess(null);
      setPasswordError(null);
      
      // Simuler un changement de mot de passe réussi
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPasswordSuccess('Mot de passe mis à jour avec succès !');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (err) {
      setPasswordError('Erreur lors du changement de mot de passe');
      console.error(err);
    } finally {
      setChangingPassword(false);
    }
  };

  return (
    <InnerPageContainer title="Profil Administrateur">
      {/* Afficher un message de succès s'il y en a un */}
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
          <FaCheckCircle className="mr-2" />
          <p>{success}</p>
        </div>
      )}

      {/* Afficher un message d'erreur s'il y en a un */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {/* Afficher un indicateur de chargement pendant le chargement des données */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <FaSpinner className="animate-spin text-blue-600 text-4xl" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Colonne de gauche - Photo de profil */}
          <div className="md:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Photo de profil</h2>
              
              <div className="flex flex-col items-center">
                <div className="relative mb-4">
                  <div className="w-40 h-40 rounded-full overflow-hidden border-4 border-gray-200">
                    {profileImage ? (
                      <img
                        src={profileImage}
                        alt="Profile"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          console.error('Erreur de chargement de l\'image:', e);
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-300 flex items-center justify-center text-gray-500">
                        <span className="text-4xl font-bold">
                          {profileData.firstName && profileData.lastName
                            ? `${profileData.firstName.charAt(0)}${profileData.lastName.charAt(0)}`
                            : 'A'}
                        </span>
                      </div>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={handleImageClick}
                    className="absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full shadow-md hover:bg-blue-700 transition-colors"
                  >
                    <FaCamera />
                  </button>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleImageUpload}
                    accept="image/*"
                    className="hidden"
                  />
                </div>
                
                <div className="text-center">
                  <h3 className="text-lg font-semibold">
                    {profileData.firstName} {profileData.lastName}
                  </h3>
                  <p className="text-gray-600">{profileData.email}</p>
                  <div className="mt-2 inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">
                    Administrateur
                  </div>
                </div>
              </div>
              
              {/* Informations supplémentaires */}
              <div className="mt-6">
                <div className="border-t pt-4">
                  <p className="text-sm text-gray-600 mb-1">
                    <span className="font-semibold">Dernière connexion:</span>{' '}
                    {profileData.lastLogin
                      ? new Date(profileData.lastLogin).toLocaleString()
                      : 'Non disponible'}
                  </p>
                  <p className="text-sm text-gray-600">
                    <span className="font-semibold">Compte créé le:</span>{' '}
                    {profileData.createdAt
                      ? new Date(profileData.createdAt).toLocaleDateString()
                      : 'Non disponible'}
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Colonne de droite - Informations personnelles et sécurité */}
          <div className="md:col-span-2">
            {/* Informations personnelles */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Informations personnelles</h2>
              
              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                      Prénom
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={profileData.firstName}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                      Nom
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={profileData.lastName}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>
                
                <div className="mb-4">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={profileData.email}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                    Numéro de téléphone
                  </label>
                  <input
                    type="tel"
                    id="phoneNumber"
                    name="phoneNumber"
                    value={profileData.phoneNumber}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Enregistrement...
                    </>
                  ) : (
                    'Enregistrer les modifications'
                  )}
                </button>
              </form>
            </div>
            
            {/* Permissions */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <div className="flex items-center mb-4">
                <FaUserShield className="text-blue-600 mr-2" />
                <h2 className="text-xl font-semibold">Permissions</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {permissionOptions.map((permission) => (
                  <div key={permission.value} className="flex items-center">
                    <input
                      type="checkbox"
                      id={permission.value}
                      name="permissions"
                      value={permission.value}
                      checked={(profileData.permissions || []).includes(permission.value)}
                      onChange={handlePermissionChange}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor={permission.value} className="ml-2 text-sm text-gray-700">
                      {permission.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Sécurité - Changement de mot de passe */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-4">
                <FaKey className="text-blue-600 mr-2" />
                <h2 className="text-xl font-semibold">Sécurité</h2>
              </div>
              
              {/* Afficher un message de succès pour le mot de passe s'il y en a un */}
              {passwordSuccess && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
                  <FaCheckCircle className="mr-2" />
                  <p>{passwordSuccess}</p>
                </div>
              )}
              
              {/* Afficher un message d'erreur pour le mot de passe s'il y en a un */}
              {passwordError && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                  <p>{passwordError}</p>
                </div>
              )}
              
              <form onSubmit={handlePasswordSubmit}>
                <div className="mb-4">
                  <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Mot de passe actuel
                  </label>
                  <input
                    type="password"
                    id="currentPassword"
                    name="currentPassword"
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Nouveau mot de passe
                  </label>
                  <input
                    type="password"
                    id="newPassword"
                    name="newPassword"
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Confirmer le nouveau mot de passe
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
                  disabled={changingPassword}
                >
                  {changingPassword ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Modification en cours...
                    </>
                  ) : (
                    'Changer le mot de passe'
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      )}
    </InnerPageContainer>
  );
};

export default ProfilePage;
