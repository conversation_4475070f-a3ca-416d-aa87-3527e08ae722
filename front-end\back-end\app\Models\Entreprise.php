<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Entreprise extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'denomination',
        'email',
        'phone_number',
        'siege_social',
        'forme_juridique',
        'activite',
        'n_rc',
        'n_cnss',
        'i_fiscale',
        'tax_professionel',
        'ice',
        'profile_image',
        'user_id',
        'gerant_name',
        'gerant_cin',
        'gerant_address',
    ];

    /**
     * Get the user that owns the entreprise profile.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
