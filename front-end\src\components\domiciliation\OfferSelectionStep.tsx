import React, { useState } from 'react';
import { DomiciliationFormData } from './DomiciliationForm';
import { FaSearch, FaFilter } from 'react-icons/fa';

interface OfferSelectionStepProps {
  formData: DomiciliationFormData;
  updateFormData: (data: Partial<DomiciliationFormData>) => void;
  onNext: () => void;
  onPrevious: () => void;
}

// Données fictives pour les offres recommandées (For You)
const recommendedOffers = [
  {
    id: 1,
    logo: "🔴⚫", // Logo simplifié pour l'exemple
    name: "Nom d'entreprise",
    ville: "Ville",
    address: "Adress",
    price: "2000DH"
  },
  {
    id: 2,
    logo: "🔵🟡", // Logo AirJob simplifié
    name: "Nom d'entreprise",
    ville: "Ville",
    address: "Adress",
    price: "2000DH"
  },
  {
    id: 3,
    logo: "🟢⚪", // Logo AC simplifié
    name: "Nom d'entreprise",
    ville: "<PERSON>",
    address: "Adress",
    price: "2000DH"
  }
];

// Données fictives pour les meilleures offres
const bestOffers = [
  {
    id: 4,
    logo: "🌳", // Logo simplifié
    name: "Nom d'entreprise",
    ville: "Ville",
    address: "Adress",
    price: "2000DH"
  },
  {
    id: 5,
    logo: "🔴🔵", // Logo simplifié
    name: "Nom d'entreprise",
    ville: "Ville",
    address: "Adress",
    price: "2000DH"
  },
  {
    id: 6,
    logo: "🔵⚪", // Logo simplifié
    name: "Nom d'entreprise",
    ville: "Ville",
    address: "Adress",
    price: "2000DH"
  }
];

const OfferSelectionStep: React.FC<OfferSelectionStepProps> = ({
  formData,
  updateFormData,
  onNext,
  onPrevious
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const handleSelectOffer = (offerId: number) => {
    updateFormData({ offreSelectionnee: offerId });
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Composant pour une carte d'offre
  const OfferCard = ({ offer }: { offer: typeof recommendedOffers[0] }) => (
    <div
      className={`bg-white rounded-xl shadow-sm overflow-hidden border ${
        formData.offreSelectionnee === offer.id ? 'border-orange-500' : 'border-gray-100'
      }`}
      onClick={() => handleSelectOffer(offer.id)}
    >
      <div className="p-4">
        <div className="flex items-center mb-2">
          <div className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 mr-3 text-xl">
            {offer.logo}
          </div>
          <div>
            <p className="font-medium text-sm">{offer.name}</p>
            <p className="text-xs text-gray-500">{offer.ville}</p>
            <p className="text-xs text-gray-500">{offer.address}</p>
          </div>
        </div>
        <div className="flex justify-between items-center mt-3">
          <p className="font-bold">{offer.price}</p>
          <button className="bg-orange-500 text-white text-xs px-4 py-1.5 rounded-full hover:bg-orange-600">
            Voir plus
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="py-4">
      {/* Barre de recherche */}
      <div className="mb-6">
        <div className="relative rounded-full border border-gray-300 flex items-center px-4 py-2 bg-white">
          <input
            type="text"
            placeholder="Choisie votre societe"
            className="flex-1 outline-none text-sm"
            value={searchTerm}
            onChange={handleSearch}
          />
          <div className="flex items-center">
            <button className="text-gray-400 hover:text-gray-600 mr-2">
              <FaSearch />
            </button>
            <button className="text-gray-400 hover:text-gray-600">
              <FaFilter />
            </button>
          </div>
        </div>
      </div>

      {/* Section "For You" */}
      <div className="mb-8">
        <h3 className="font-bold text-lg mb-4">For You</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {recommendedOffers.map(offer => (
            <OfferCard key={offer.id} offer={offer} />
          ))}
        </div>
      </div>

      {/* Section "Meilleurs offres" */}
      <div className="mb-8">
        <h3 className="font-bold text-lg mb-4">Meilleurs offres</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {bestOffers.map(offer => (
            <OfferCard key={offer.id} offer={offer} />
          ))}
        </div>
      </div>

      <div className="flex justify-between mt-8">
        <button
          type="button"
          onClick={onPrevious}
          className="px-6 py-2 border border-gray-300 rounded-full text-gray-700 font-medium hover:bg-gray-50"
        >
          Précédent
        </button>

        <button
          type="button"
          onClick={onNext}
          disabled={!formData.offreSelectionnee}
          className={`px-6 py-2 rounded-full text-white font-medium ${
            formData.offreSelectionnee ? 'bg-orange-500 hover:bg-orange-600' : 'bg-gray-300 cursor-not-allowed'
          }`}
        >
          Suivant
        </button>
      </div>
    </div>
  );
};

export default OfferSelectionStep;
