<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coursier extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone_number',
        'address',
        'vehicle_type',
        'license_number',
        'profile_image',
        'user_id',
    ];

    /**
     * Get the user that owns the coursier profile.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
