import Section from "./Section";
import { IconType } from "react-icons";

interface SectionProps {
  icon: IconType;
  label: string;
  link: string;
}

interface SidebarProps {
  sections: SectionProps[];
}

function Sidebar({ sections }: SidebarProps) {
  return (
    <div className="flex flex-col w-62 h-full pt-12 shadow-xl rounded-br-3xl bg-blue-100">
      {sections.map((section, index) => (
        <Section
          key={index}
          icon={<section.icon />}
          label={section.label}
          link={section.link}
        />
      ))}
    </div>
  );
}

export default Sidebar;
