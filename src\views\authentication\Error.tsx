import { FC } from 'react';
import { Box, Container, Typography, Button, useTheme, alpha } from '@mui/material';
import { Link } from 'react-router';
import { IconHome, IconPlus, IconCircle } from '@tabler/icons-react';

const Error: FC = () => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `
            radial-gradient(circle at 20% 80%, ${alpha('#ffffff', 0.1)} 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, ${alpha('#ffffff', 0.1)} 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, ${alpha('#ffffff', 0.05)} 0%, transparent 50%)
          `,
        },
      }}
    >
      {/* Decorative elements */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          left: '10%',
          width: '100px',
          height: '100px',
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        <IconPlus size={24} color="white" />
      </Box>
      <Box
        sx={{
          position: 'absolute',
          top: '20%',
          right: '15%',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        {[...Array(9)].map((_, i) => (
          <Box key={i} sx={{ width: '4px', height: '4px', bgcolor: 'white', borderRadius: '50%' }} />
        ))}
      </Box>
      <Box
        sx={{
          position: 'absolute',
          bottom: '30%',
          left: '5%',
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        <IconCircle size={20} color="white" />
      </Box>
      <Box
        sx={{
          position: 'absolute',
          bottom: '10%',
          right: '20%',
          width: '50px',
          height: '50px',
          opacity: 0.1,
          zIndex: 1,
        }}
      >
        <IconPlus size={16} color="white" />
      </Box>

      <Container maxWidth="md" sx={{ textAlign: 'center', zIndex: 2 }}>
        {/* Large 404 */}
        <Typography
          variant="h1"
          sx={{
            fontSize: { xs: '8rem', md: '12rem' },
            fontWeight: 900,
            color: 'rgba(255, 255, 255, 0.1)',
            lineHeight: 1,
            mb: -2,
            textShadow: '0 0 50px rgba(255, 255, 255, 0.1)',
          }}
        >
          404
        </Typography>

        <Typography
          variant="h2"
          sx={{
            color: 'white',
            fontWeight: 700,
            fontSize: { xs: '2rem', md: '3rem' },
            mb: 2,
            lineHeight: 1.2,
          }}
        >
          Oops! Page not found
        </Typography>

        <Typography
          variant="h6"
          sx={{
            color: alpha('#ffffff', 0.9),
            fontSize: '1.1rem',
            maxWidth: '500px',
            mx: 'auto',
            mb: 4,
            lineHeight: 1.6,
          }}
        >
          The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
        </Typography>

        <Button
          component={Link}
          to="/"
          variant="contained"
          size="large"
          startIcon={<IconHome size={20} />}
          sx={{
            py: 1.5,
            px: 4,
            borderRadius: 3,
            background: 'rgba(255, 255, 255, 0.95)',
            color: '#2d3748',
            textTransform: 'none',
            fontSize: '1rem',
            fontWeight: 600,
            boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha('#ffffff', 0.2)}`,
            '&:hover': {
              background: 'rgba(255, 255, 255, 1)',
              boxShadow: '0 6px 20px rgba(0, 0, 0, 0.15)',
              transform: 'translateY(-2px)',
            },
            transition: 'all 0.3s ease',
          }}
        >
          Go Back to Home
        </Button>

        {/* Floating animation styles */}
        <style>
          {`
            @keyframes float {
              0%, 100% { transform: translateY(0px) rotate(0deg); }
              50% { transform: translateY(-20px) rotate(10deg); }
            }
          `}
        </style>
      </Container>
    </Box>
  );
};

export default Error;
