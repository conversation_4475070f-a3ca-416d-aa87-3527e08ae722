import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { ClientProfile, CoursierProfile, EntrepriseProfile } from './profileService';

// Define the base URL for the API
const API_URL = 'http://localhost:8000/api';

// Create an axios instance with default configurations
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 seconds timeout
});

// Add a request interceptor to include the auth token in all requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add a response interceptor to log responses and errors
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url, response.data);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.message);
    if (error.response) {
      console.error('Error Data:', error.response.data);
      console.error('Error Status:', error.response.status);
    }
    return Promise.reject(error);
  }
);

// User interface
export interface User {
  id: number;
  name: string;
  email: string;
  role: 'client' | 'coursier' | 'entreprise' | 'admin';
  email_verified_at?: string;
  created_at?: string;
  updated_at?: string;
}

// Auth interfaces
export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  role: 'client' | 'coursier' | 'entreprise' | 'admin';
}

export interface AuthResponse {
  user: User;
  token: string;
  token_type: string;
}

export interface ChangePasswordData {
  current_password: string;
  new_password: string;
  new_password_confirmation: string;
}

// Auth service
export const authService = {
  // Register a new user
  register: async (data: RegisterData): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>('/register', data);
    return response.data;
  },

  // Login a user
  login: async (data: LoginData): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>('/login', data);
    return response.data;
  },

  // Get the authenticated user
  getUser: async (): Promise<User> => {
    const response = await api.get<User>('/user');
    return response.data;
  },

  // Logout the user
  logout: async (): Promise<void> => {
    await api.post('/logout');
  },

  // Change the user's password
  changePassword: async (data: ChangePasswordData): Promise<{ message: string }> => {
    const response = await api.post<{ message: string }>('/change-password', data);
    return response.data;
  },
};

// Client service
export const clientService = {
  // Get all clients
  getAll: async (): Promise<ClientProfile[]> => {
    const response = await api.get<ClientProfile[]>('/clients');
    return response.data;
  },

  // Get a specific client
  get: async (id: number): Promise<ClientProfile> => {
    const response = await api.get<ClientProfile>(`/clients/${id}`);
    return response.data;
  },

  // Get the authenticated client's profile
  getProfile: async (): Promise<ClientProfile> => {
    const response = await api.get<ClientProfile>('/clients/profile');
    return response.data;
  },

  // Create a new client
  create: async (data: Omit<ClientProfile, 'id'>): Promise<ClientProfile> => {
    const response = await api.post<ClientProfile>('/clients', data);
    return response.data;
  },

  // Update a client
  update: async (id: number, data: Partial<ClientProfile>): Promise<ClientProfile> => {
    const response = await api.put<ClientProfile>(`/clients/${id}`, data);
    return response.data;
  },

  // Update a client's profile image
  updateProfileImage: async (id: number, file: File): Promise<{ message: string; profile_image: string }> => {
    const formData = new FormData();
    formData.append('profile_image', file);

    const response = await api.post<{ message: string; profile_image: string }>(
      `/clients/${id}/profile-image`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  },

  // Delete a client
  delete: async (id: number): Promise<{ message: string }> => {
    const response = await api.delete<{ message: string }>(`/clients/${id}`);
    return response.data;
  },
};

// Coursier service
export const coursierService = {
  // Get all coursiers
  getAll: async (): Promise<CoursierProfile[]> => {
    const response = await api.get<CoursierProfile[]>('/coursiers');
    return response.data;
  },

  // Get a specific coursier
  get: async (id: number): Promise<CoursierProfile> => {
    const response = await api.get<CoursierProfile>(`/coursiers/${id}`);
    return response.data;
  },

  // Get the authenticated coursier's profile
  getProfile: async (): Promise<CoursierProfile> => {
    const response = await api.get<CoursierProfile>('/coursiers/profile');
    return response.data;
  },

  // Create a new coursier
  create: async (data: Omit<CoursierProfile, 'id'>): Promise<CoursierProfile> => {
    const response = await api.post<CoursierProfile>('/coursiers', data);
    return response.data;
  },

  // Update a coursier
  update: async (id: number, data: Partial<CoursierProfile>): Promise<CoursierProfile> => {
    const response = await api.put<CoursierProfile>(`/coursiers/${id}`, data);
    return response.data;
  },

  // Update a coursier's profile image
  updateProfileImage: async (id: number, file: File): Promise<{ message: string; profile_image: string }> => {
    const formData = new FormData();
    formData.append('profile_image', file);

    const response = await api.post<{ message: string; profile_image: string }>(
      `/coursiers/${id}/profile-image`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  },

  // Delete a coursier
  delete: async (id: number): Promise<{ message: string }> => {
    const response = await api.delete<{ message: string }>(`/coursiers/${id}`);
    return response.data;
  },
};

// Entreprise service
export const entrepriseService = {
  // Get all entreprises
  getAll: async (): Promise<EntrepriseProfile[]> => {
    const response = await api.get<EntrepriseProfile[]>('/entreprises');
    return response.data;
  },

  // Get a specific entreprise
  get: async (id: number): Promise<EntrepriseProfile> => {
    const response = await api.get<EntrepriseProfile>(`/entreprises/${id}`);
    return response.data;
  },

  // Get the authenticated entreprise's profile
  getProfile: async (): Promise<EntrepriseProfile> => {
    const response = await api.get<EntrepriseProfile>('/entreprises/profile');
    return response.data;
  },

  // Create a new entreprise
  create: async (data: Omit<EntrepriseProfile, 'id'>): Promise<EntrepriseProfile> => {
    const response = await api.post<EntrepriseProfile>('/entreprises', data);
    return response.data;
  },

  // Update an entreprise
  update: async (id: number, data: Partial<EntrepriseProfile>): Promise<EntrepriseProfile> => {
    const response = await api.put<EntrepriseProfile>(`/entreprises/${id}`, data);
    return response.data;
  },

  // Update an entreprise's profile image
  updateProfileImage: async (id: number, file: File): Promise<{ message: string; profile_image: string }> => {
    const formData = new FormData();
    formData.append('profile_image', file);

    const response = await api.post<{ message: string; profile_image: string }>(
      `/entreprises/${id}/profile-image`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  },

  // Delete an entreprise
  delete: async (id: number): Promise<{ message: string }> => {
    const response = await api.delete<{ message: string }>(`/entreprises/${id}`);
    return response.data;
  },
};

// Export a default object with all services
export default {
  auth: authService,
  client: clientService,
  coursier: coursierService,
  entreprise: entrepriseService,
};
