import React from 'react';

const DashboardHomePage = () => {
  // Exemple de statistiques
  const stats = [
    { label: "Commandes en attente", value: 5, color: "bg-yellow-500" },
    { label: "Commandes livrées", value: 28, color: "bg-green-500" },
    { label: "Revenus du mois", value: "1,250 DH", color: "bg-blue-500" },
  ];

  // Exemple d'activités récentes
  const activities = [
    { id: 1, action: "Commande #1234 livrée", time: "Il y a 2 heures" },
    { id: 2, action: "Nouvelle commande #1235 assignée", time: "Il y a 3 heures" },
    { id: 3, action: "Paiement reçu pour la commande #1230", time: "Il y a 1 jour" },
    { id: 4, action: "Commande #1228 livrée", time: "Il y a 2 jours" },
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Tableau de bord Coursier</h1>
      
      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow-md border-l-4 border-blue-500">
            <h2 className="text-gray-500 text-sm uppercase font-semibold">{stat.label}</h2>
            <p className="text-3xl font-bold mt-2">{stat.value}</p>
            <div className={`w-full h-2 ${stat.color} mt-4 rounded-full`}></div>
          </div>
        ))}
      </div>
      
      {/* Activités récentes */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Activités récentes</h2>
        <div className="space-y-4">
          {activities.map(activity => (
            <div key={activity.id} className="flex justify-between items-center border-b pb-3">
              <div>
                <p className="font-medium">{activity.action}</p>
                <p className="text-sm text-gray-500">{activity.time}</p>
              </div>
              <button className="text-blue-500 hover:text-blue-700">
                Voir détails
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DashboardHomePage;
