import React from 'react';
import { DomiciliationFormData } from './DomiciliationForm';

interface ConfirmationStepProps {
  formData: DomiciliationFormData;
  onSubmit: () => void;
  onPrevious: () => void;
}

// Données fictives pour les offres (pour afficher le nom de l'offre sélectionnée)
const allOffers = [
  { id: 1, name: "Nom d'entreprise", ville: "Ville", address: "Adress", price: "2000DH" },
  { id: 2, name: "Nom d'entreprise", ville: "Ville", address: "Adress", price: "2000DH" },
  { id: 3, name: "Nom d'entreprise", ville: "Ville", address: "Adress", price: "2000DH" },
  { id: 4, name: "Nom d'entreprise", ville: "Ville", address: "Adress", price: "2000DH" },
  { id: 5, name: "Nom d'entreprise", ville: "Ville", address: "Adress", price: "2000DH" },
  { id: 6, name: "Nom d'entreprise", ville: "<PERSON>", address: "Adress", price: "2000DH" }
];

const ConfirmationStep: React.FC<ConfirmationStepProps> = ({
  formData,
  onSubmit,
  onPrevious
}) => {
  const selectedOffer = allOffers.find(offer => offer.id === formData.offreSelectionnee);

  return (
    <div className="py-4">
      <h2 className="text-xl font-semibold text-center text-orange-500 mb-6">
        Confirmation
      </h2>

      <div className="bg-gray-50 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4">Récapitulatif de votre demande</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Informations personnelles</h4>
            <div className="space-y-2">
              <p><span className="text-gray-500">Nom:</span> {formData.nom}</p>
              <p><span className="text-gray-500">Prénom:</span> {formData.prenom}</p>
              <p><span className="text-gray-500">CIN:</span> {formData.cin}</p>
              <p><span className="text-gray-500">Adresse:</span> {formData.adresseGerant}</p>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-700 mb-2">Informations entreprise</h4>
            <div className="space-y-2">
              <p><span className="text-gray-500">Nom d'entreprise:</span> {formData.nomEntreprise}</p>
              <p><span className="text-gray-500">ICE:</span> {formData.ice}</p>
            </div>

            <h4 className="font-medium text-gray-700 mt-4 mb-2">Offre sélectionnée</h4>
            <div className="bg-white border border-orange-200 rounded-md p-3">
              <div className="flex items-center">
                <div className="mr-3">
                  <p className="font-semibold text-orange-600">{selectedOffer?.name}</p>
                  <p className="text-sm text-gray-500">{selectedOffer?.ville}</p>
                  <p className="text-sm text-gray-500">{selectedOffer?.address}</p>
                </div>
              </div>
              <p className="font-bold text-gray-700 mt-2">{selectedOffer?.price}</p>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-200 pt-4">
          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="terms"
              className="h-4 w-4 text-orange-500 focus:ring-orange-400 border-gray-300 rounded"
            />
            <label htmlFor="terms" className="ml-2 block text-sm text-gray-700">
              J'accepte les <a href="#" className="text-orange-500 hover:underline">termes et conditions</a>
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="privacy"
              className="h-4 w-4 text-orange-500 focus:ring-orange-400 border-gray-300 rounded"
            />
            <label htmlFor="privacy" className="ml-2 block text-sm text-gray-700">
              J'accepte la <a href="#" className="text-orange-500 hover:underline">politique de confidentialité</a>
            </label>
          </div>
        </div>
      </div>

      <div className="flex justify-between mt-8">
        <button
          type="button"
          onClick={onPrevious}
          className="px-6 py-2 border border-gray-300 rounded-full text-gray-700 font-medium hover:bg-gray-50"
        >
          Précédent
        </button>

        <button
          type="button"
          onClick={onSubmit}
          className="px-6 py-2 rounded-full text-white font-medium bg-orange-500 hover:bg-orange-600"
        >
          Confirmer
        </button>
      </div>
    </div>
  );
};

export default ConfirmationStep;
