import { uniqueId } from 'lodash';

interface MenuitemsType {
  [x: string]: any;
  id?: string;
  navlabel?: boolean;
  subheader?: string;
  title?: string;
  icon?: any;
  href?: string;
  children?: MenuitemsType[];
  chip?: string;
  chipColor?: string;
  variant?: string;
  external?: boolean;
  roles?: string[]; // Nouveau: pour filtrer par rôle
}

import {
  IconDashboard,
  IconTruck,
  IconBuildingStore,
  IconUser,
  IconPackage,
  IconCalculator,
  IconMapPin,
  IconFileText,
  IconSettings,
  IconLogout,
  IconHome,
  IconAperture,
} from '@tabler/icons-react';

// Fonction pour obtenir les menus basés sur le rôle utilisateur
export const getMenuItemsByRole = (userRole?: string): MenuitemsType[] => {
  const baseMenus: MenuitemsType[] = [
    {
      navlabel: true,
      subheader: 'Principal',
    },
    {
      id: uniqueId(),
      title: 'Dashboard',
      icon: IconDashboard,
      href: `/${userRole}/dashboard`,
      roles: ['client', 'coursier', 'entreprise', 'admin'],
    },
  ];

  // Menus spécifiques par rôle
  const roleSpecificMenus: { [key: string]: MenuitemsType[] } = {
    client: [
      {
        navlabel: true,
        subheader: 'Services',
      },
      {
        id: uniqueId(),
        title: 'Mes Commandes',
        icon: IconPackage,
        href: '/client/commandes',
      },
      {
        id: uniqueId(),
        title: 'Nouvelle Livraison',
        icon: IconTruck,
        href: '/client/nouvelle-livraison',
        chip: 'Nouveau',
        chipColor: 'primary',
      },
      {
        id: uniqueId(),
        title: 'Suivi des Colis',
        icon: IconMapPin,
        href: '/client/suivi',
      },
      {
        navlabel: true,
        subheader: 'Compte',
      },
      {
        id: uniqueId(),
        title: 'Mon Profil',
        icon: IconUser,
        href: '/client/profil',
      },
    ],
    coursier: [
      {
        navlabel: true,
        subheader: 'Livraisons',
      },
      {
        id: uniqueId(),
        title: 'Commandes Disponibles',
        icon: IconPackage,
        href: '/coursier/commandes-disponibles',
        chip: 'Nouveau',
        chipColor: 'warning',
      },
      {
        id: uniqueId(),
        title: 'Mes Livraisons',
        icon: IconTruck,
        href: '/coursier/mes-livraisons',
      },
      {
        id: uniqueId(),
        title: 'Historique',
        icon: IconFileText,
        href: '/coursier/historique',
      },
      {
        navlabel: true,
        subheader: 'Compte',
      },
      {
        id: uniqueId(),
        title: 'Mes Gains',
        icon: IconCalculator,
        href: '/coursier/gains',
      },
      {
        id: uniqueId(),
        title: 'Mon Profil',
        icon: IconUser,
        href: '/coursier/profil',
      },
    ],
    entreprise: [
      {
        navlabel: true,
        subheader: 'Services',
      },
      {
        id: uniqueId(),
        title: 'Comptabilité',
        icon: IconCalculator,
        href: '/entreprise/comptabilite',
      },
      {
        id: uniqueId(),
        title: 'Domiciliation',
        icon: IconMapPin,
        href: '/entreprise/domiciliation',
      },
      {
        id: uniqueId(),
        title: 'Services Juridiques',
        icon: IconFileText,
        href: '/entreprise/juridique',
      },
      {
        id: uniqueId(),
        title: 'Documents',
        icon: IconFileText,
        href: '/entreprise/documents',
      },
      {
        navlabel: true,
        subheader: 'Gestion',
      },
      {
        id: uniqueId(),
        title: 'Mon Entreprise',
        icon: IconBuildingStore,
        href: '/entreprise/profil',
      },
    ],
    admin: [
      {
        navlabel: true,
        subheader: 'Gestion',
      },
      {
        id: uniqueId(),
        title: 'Utilisateurs',
        icon: IconUser,
        href: '/admin/utilisateurs',
      },
      {
        id: uniqueId(),
        title: 'Commandes',
        icon: IconPackage,
        href: '/admin/commandes',
      },
      {
        id: uniqueId(),
        title: 'Coursiers',
        icon: IconTruck,
        href: '/admin/coursiers',
      },
      {
        id: uniqueId(),
        title: 'Entreprises',
        icon: IconBuildingStore,
        href: '/admin/entreprises',
      },
      {
        navlabel: true,
        subheader: 'Système',
      },
      {
        id: uniqueId(),
        title: 'Paramètres',
        icon: IconSettings,
        href: '/admin/parametres',
      },
    ],
  };

  // Menus communs à tous les rôles
  const commonMenus: MenuitemsType[] = [
    {
      navlabel: true,
      subheader: 'Autres',
    },
    {
      id: uniqueId(),
      title: 'Paramètres',
      icon: IconSettings,
      href: `/${userRole}/parametres`,
      roles: ['client', 'coursier', 'entreprise'],
    },
  ];

  // Combiner les menus
  let allMenus = [...baseMenus];

  if (userRole && roleSpecificMenus[userRole]) {
    allMenus = [...allMenus, ...roleSpecificMenus[userRole]];
  }

  allMenus = [...allMenus, ...commonMenus];

  return allMenus;
};

// Menu par défaut (pour la compatibilité)
const Menuitems: MenuitemsType[] = [
  {
    navlabel: true,
    subheader: 'Accueil',
  },
  {
    id: uniqueId(),
    title: 'Page d\'accueil',
    icon: IconHome,
    href: '/',
  },
  {
    id: uniqueId(),
    title: 'Sample Page',
    icon: IconAperture,
    href: '/sample/page',
    chip: 'Demo',
    chipColor: 'secondary',
  },
];

export default Menuitems;
