import Sidebar from "../../components/Sidebar";
import {FaMoneyBillWave} from "react-icons/fa";
import {MdOutlineGridView, MdOutlineNotificationsNone} from "react-icons/md";
import {FiUser} from "react-icons/fi";
import {FaArrowTrendUp} from "react-icons/fa6";
import {BiSolidOffer} from "react-icons/bi";
import { Outlet } from "react-router-dom"; // Import Outlet


// Define the sections of the sidebar
const sections = [
    { icon: MdOutlineGridView, label: "dashboard", link: "/client/dashboard" },
    { icon: FiUser, label: "profile", link: "/client/profile" },
    { icon: MdOutlineNotificationsNone, label: "notification", link: "/client/notification" },
    {icon: FaArrowTrendUp, label:"comptabilite" ,link: "/client/comptabilite"},
    {icon: BiSolidOffer, label:"offers" ,link: "/client/offers"},
];

export const ClientLayout=()=>{

    return(

        <div className="flex h-screen">
            {/* Sidebar */}
            <Sidebar sections={sections} />

            {/* Main Content */}
            <div className="bg-gray-300 flex-1 p-4">
                <Outlet />
                {/* Add main content here */}
            </div>
        </div>

    );
}