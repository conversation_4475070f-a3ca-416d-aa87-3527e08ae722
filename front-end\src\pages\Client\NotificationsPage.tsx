import React from 'react';

const NotificationsPage = () => {
  // Exemple de notifications
  const notifications = [
    {
      id: 1,
      title: "Nouvelle offre disponible",
      message: "Une nouvelle offre de domiciliation est disponible dans votre région.",
      date: "2023-06-15",
      read: false
    },
    {
      id: 2,
      title: "Rappel de paiement",
      message: "Votre paiement mensuel est prévu pour demain.",
      date: "2023-06-10",
      read: true
    },
    {
      id: 3,
      title: "Mise à jour des conditions",
      message: "Nos conditions générales ont été mises à jour. Veuillez les consulter.",
      date: "2023-06-05",
      read: true
    }
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Notifications</h1>
      <p className="mb-6">Consultez vos dernières notifications et mises à jour.</p>
      
      <div className="space-y-4">
        {notifications.map(notification => (
          <div 
            key={notification.id} 
            className={`p-4 border rounded-lg ${notification.read ? 'bg-white' : 'bg-blue-50 border-blue-200'}`}
          >
            <div className="flex justify-between items-start">
              <h3 className={`font-semibold ${notification.read ? '' : 'text-blue-700'}`}>{notification.title}</h3>
              <span className="text-sm text-gray-500">{notification.date}</span>
            </div>
            <p className="mt-2 text-gray-600">{notification.message}</p>
            {!notification.read && (
              <button className="mt-2 text-sm text-blue-600 hover:text-blue-800">
                Marquer comme lu
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default NotificationsPage;
