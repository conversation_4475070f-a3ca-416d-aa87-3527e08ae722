// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import React, { lazy } from 'react';
import { createBrowserRouter, Navigate } from 'react-router';
import Loadable from '../layouts/full/shared/loadable/Loadable';

/* ***Layouts**** */
const FullLayout = Loadable(lazy(() => import('../layouts/full/FullLayout')));
const BlankLayout = Loadable(lazy(() => import('../layouts/blank/BlankLayout')));

/* ****Pages***** */
// Landing Page
const LandingPage = Loadable(lazy(() => import('../views/landing/LandingPage')));

// Authentication Pages
const Login = Loadable(lazy(() => import('../views/authentication/Login')));
const Register = Loadable(lazy(() => import('../views/authentication/Register')));
const ForgotPassword = Loadable(lazy(() => import('../views/authentication/ForgotPassword')));
const Error = Loadable(lazy(() => import('../views/authentication/Error')));

// Dashboard Pages
const ClientDashboard = Loadable(lazy(() => import('../views/dashboards/client/ClientDashboard')));
const CoursierDashboard = Loadable(lazy(() => import('../views/dashboards/coursier/CoursierDashboard')));
const EntrepriseDashboard = Loadable(lazy(() => import('../views/dashboards/entreprise/EntrepriseDashboard')));

// Redirect Component
const RoleBasedRedirect = Loadable(lazy(() => import('../components/RoleBasedRedirect')));

// Protected Route Component
const ProtectedRoute = Loadable(lazy(() => import('../components/ProtectedRoute')));

// Sample Page (kept for reference)
const SamplePage = Loadable(lazy(() => import('../views/sample-page/SamplePage')));

// Test Page
const TestPage = Loadable(lazy(() => import('../views/test/TestPage')));

const Router = [
  // Landing Page Route (Public)
  {
    path: '/',
    element: <BlankLayout />,
    children: [
      { path: '/', element: <LandingPage /> },
    ],
  },

  // Authentication Routes (Public)
  {
    path: '/auth',
    element: <BlankLayout />,
    children: [
      { path: 'login', element: <Login /> },
      { path: 'register', element: <Register /> },
      { path: 'forgot-password', element: <ForgotPassword /> },
      { path: '404', element: <Error /> },
      { path: '*', element: <Navigate to="/auth/404" /> },
    ],
  },

  // Dashboard Redirect Route
  {
    path: '/dashboard',
    element: <RoleBasedRedirect />,
  },

  // Client Dashboard Routes (Protected)
  {
    path: '/client',
    element: (
      <ProtectedRoute allowedRoles={['client']}>
        <FullLayout />
      </ProtectedRoute>
    ),
    children: [
      { path: 'dashboard', element: <ClientDashboard /> },
      { path: '*', element: <Navigate to="/client/dashboard" /> },
    ],
  },

  // Coursier Dashboard Routes (Protected)
  {
    path: '/coursier',
    element: (
      <ProtectedRoute allowedRoles={['coursier']}>
        <FullLayout />
      </ProtectedRoute>
    ),
    children: [
      { path: 'dashboard', element: <CoursierDashboard /> },
      { path: '*', element: <Navigate to="/coursier/dashboard" /> },
    ],
  },

  // Entreprise Dashboard Routes (Protected)
  {
    path: '/entreprise',
    element: (
      <ProtectedRoute allowedRoles={['entreprise']}>
        <FullLayout />
      </ProtectedRoute>
    ),
    children: [
      { path: 'dashboard', element: <EntrepriseDashboard /> },
      { path: '*', element: <Navigate to="/entreprise/dashboard" /> },
    ],
  },

  // Test Page Route
  {
    path: '/test',
    element: <BlankLayout />,
    children: [
      { path: '', element: <TestPage /> },
    ],
  },

  // Sample Page Route (for reference)
  {
    path: '/sample',
    element: <FullLayout />,
    children: [
      { path: 'page', element: <SamplePage /> },
    ],
  },

  // Catch all route
  {
    path: '*',
    element: <Navigate to="/" />,
  },
];

const router = createBrowserRouter(Router);
export default router;
