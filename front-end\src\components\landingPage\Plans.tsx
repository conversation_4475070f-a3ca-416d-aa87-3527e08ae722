import {useNavigate} from 'react-router-dom';


const Plans=()=>{
    const navigate=useNavigate();
    return (
        <div className="relative">
            {/**/}
            
            <img className="overlay-image" src={"/src/assets/shapes/shape1.svg"}/>
            

            <div className={"bg-[#D3E9FF] p-10 space-y-15 pb-20"}>
                <h3 className={"text-[#11314A] font-bold ml-20 pb-20 text-[36px]"}>The best plans for you with the best pricing</h3>
                {/* Cards Section */}
                <div className="flex flex-col md:flex-row w-full px-20 space-x-5 gap-3 relative z-10">
                    <div className="card bg-gradient-to-b from-[#C8C8C885] to-white w-full md:w-1/3 lg:w-1/3 shadow-xl">
                        <div className="card-body items-center text-center text-[#132A3C]">
                            <div className="card-title">
                                <div className="stats bg-opacity-0 ">
                                    <div className="stat text-[#132A3C] items-center">
                                        <div className="stat-title border-[#132A3C] border-[1px] mb-[10px]
                                        rounded-[25px] p-[10px] text-[#132A3C]">Basic Plan</div>
                                        <div className="stat-value ">399 Dh</div>
                                        <div className="stat-desc text-[#132A3C]">Par Mois</div>
                                    </div>


                                </div>
                            </div>
                            <p>Basic features for up to 10 usBasic features for up to 10 users</p>
                            <br/>
                            {/* Plan Description */}
                            <div className={"flex flex-col space-y-4"}>
                                <div className={"flex space-x-3 flex-row"}>
                                    <img src={"/src/assets/icons/check.svg"} />
                                    <p>Basic features for up to 10 users for up to 10 users</p>
                                </div>
                                <div className={"flex space-x-3  flex-row"}>
                                    <img src={"/src/assets/icons/check.svg"} />
                                    <p>Basic features for up to 10 users for up to 10 users</p>
                                </div>
                                <div className={"flex space-x-3  flex-row"}>
                                    <img src={"/src/assets/icons/check.svg"} />
                                    <p>Basic features for up to 10 users for up to 10 users</p>
                                </div>
                            </div>
                            <div className="card-actions">
                                <button className="btn btn-primary" onClick={() => navigate('/signin')}>Start 7-Day Free</button>
                            </div>
                        </div>
                    </div>
                    <div className="card bg-gradient-to-r from-[#75E1FFD9] via-[#33A4C39C] to-[#1E8EADD9] w-full md:w-1/3 lg:w-1/3 shadow-xl">
                        <div className="card-body items-center text-center text-white">
                            <div className="card-title">
                                <div className="stats bg-opacity-0">
                                    <div className="stat">
                                        <div className="stat-title border-[#132A3C] border-[1px] mb-[10px]
                                        rounded-[25px] p-[10px] text-[#132A3C]">Premiume plan</div>
                                        <div className="stat-value text-[#132A3C]">999 Dh</div>
                                        <div className="stat-desc text-[#132A3C]">Par Mois</div>
                                    </div>
                                </div>
                            </div>
                            <p>Basic features for up to 10 usBasic features for up to 10 users</p>
                            <br/>
                            {/* Plan Description */}
                            <div className={"flex flex-col space-y-4"}>
                                <div className={"flex space-x-3 flex-row"}>
                                    <img src={"/src/assets/icons/check.svg"} />
                                    <p>Basic features for up to 10 users for up to 10 users</p>
                                </div>
                                <div className={"flex space-x-3 flex-row"}>
                                    <img src={"/src/assets/icons/check.svg"} />
                                    <p>Basic features for up to 10 users for up to 10 users</p>
                                </div>
                                <div className={"flex space-x-3 flex-row"}>
                                    <img src={"/src/assets/icons/check.svg"} />
                                    <p>Basic features for up to 10 users for up to 10 users</p>
                                </div>
                            </div>
                            <div className="card-actions">
                                <button className="btn btn-primary" onClick={() => navigate('/signin')}>Start 7-Day Free</button>
                            </div>
                        </div>
                    </div>
                    <div className="card bg-gradient-to-t from-white via-[#B8E0FF75] to-[#83C8FF] w-full md:w-1/3 lg:w-1/3 shadow-xl">
                        <div className="card-body items-center text-center text-[#132A3C]">
                            <div className="card-title ">
        
                                    <div className="stat bg-opacity-0">
                                        <div className="stat-title border-[#132A3C] border-[1px] mb-[10px]
                                        rounded-[25px] p-[10px] text-[#132A3C]">Standard plan</div>
                                        <div className="stat-value ">599 Dh</div>
                                        <div className="stat-desc text-[#132A3C]">Par Mois</div>
                                    </div>
                                
                            </div>
                            <p>Basic features for up to 10 usBasic features for up to 10 users</p>
                            <br/>
                            {/* Plan Description */}
                            <div className={"flex flex-col space-y-4"}>
                                <div className={"flex space-x-3 flex-row"}>
                                    <img src={"/src/assets/icons/check.svg"} />
                                    <p>Basic features for up to 10 users for up to 10 users</p>
                                </div>
                                <div className={"flex space-x-3 flex-row"}>
                                    <img src={"/src/assets/icons/check.svg"} />
                                    <p>Basic features for up to 10 users for up to 10 users</p>
                                </div>
                                <div className={"flex space-x-3 flex-row"}>
                                    <img src={"/src/assets/icons/check.svg"} />
                                    <p>Basic features for up to 10 users for up to 10 users</p>
                                </div>
                            </div>
                            <div className="card-actions">
                                <button className="btn btn-primary" onClick={() => navigate('/signin')}>Start 7-Day Free</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Plans;