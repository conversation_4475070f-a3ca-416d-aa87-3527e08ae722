import { useState } from "react";
import { FaLifeRing, FaMoneyBillWave, FaClipboardList, FaHome, FaUser } from "react-icons/fa";
import { Outlet } from "react-router-dom";

import NavBar from "../../components/NavBar";
import CoursierSidebar from "../../components/Coursier/CoursierSidebar";
import { useAuthContext } from "../../contexts/AuthContext";

// Import all pages that will be displayed in the dashboard
import DashboardHomePage from "./DashboardHomePage";
import CoursierProfilePage from "./ProfilePage";
import CommandesPage from "./CommandesPage";
import ArgentPage from "./ArgentPage";
import SupportPage from "../SupportPage";

// Define the sections of the sidebar
const sections = [
  { icon: FaHome, label: "Dashboard", link: "/coursier/dashboard" },
  { icon: FaUser, label: "Profil", link: "/coursier/profile" },
  { icon: FaClipboardList, label: "Commandes", link: "/coursier/commandes" },
  { icon: FaMoneyBillWave, label: "L'argent", link: "/coursier/argent" },
  { icon: FaLifeRing, label: "Support", link: "/coursier/support" },
];

const DashboardCoursier = () => {
  const [currentPage, setCurrentPage] = useState("/coursier/dashboard");
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuthContext();

  const handleLinkClick = (link: string) => {
    if (link !== currentPage) {
      setIsLoading(true);
      setCurrentPage(link);
      // Simuler un temps de chargement court
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    }
  };

  // Fonction pour rendre le composant approprié en fonction de la page actuelle
  const renderCurrentPage = () => {
    switch (currentPage) {
      case "/coursier/dashboard":
        return <DashboardHomePage />;
      case "/coursier/profile":
        return <CoursierProfilePage />;
      case "/coursier/commandes":
        return <CommandesPage />;
      case "/coursier/argent":
        return <ArgentPage />;
      case "/coursier/support":
        return <SupportPage />;
      default:
        return <DashboardHomePage />;
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <NavBar
        title="Dashboard Coursier"
        userName={user?.name}
        userImage={user?.profileImage}
        onProfileClick={handleLinkClick}
      />
      <div className="flex flex-1 overflow-auto">
        {/* Sidebar */}
        <CoursierSidebar
          sections={sections}
          onLinkClick={handleLinkClick}
        />

        {/* Main Content */}
        <div className="flex-1 p-4 overflow-auto">
          {isLoading ? (
            <div className="w-full h-full flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="w-full bg-white rounded-lg shadow-md p-4">
              {renderCurrentPage()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardCoursier;
