import ServiceCard from './ServiceCard';

const OurServices=()=>{
    const services = [
        {
            title: "Domicialisation",
            description: "This is a template Figma file, turned into code using Anima. Learn more at AnimaApp.com",
            icon: "https://cdn.builder.io/api/v1/image/assets/TEMP/193f7437b5564a6af14004e6efd5ef9a0b232214fd995d870062ce397f0b50d2?placeholderIfAbsent=true&apiKey=b7978f95452b450fbd6d95e74f4b2ae2"
        },
        {
            title: "Juridique entreprise",
            description: "This is a template Figma file, turned into code using Anima. Learn more at AnimaApp.com",
            icon: "https://cdn.builder.io/api/v1/image/assets/TEMP/fa5b7f40d2ce2c2dcd0cb52c3637acd91ef6407a54640f4d58e427a6f9ceda3a?placeholderIfAbsent=true&apiKey=b7978f95452b450fbd6d95e74f4b2ae2"
        },
        {
            title: "Accompagnement",
            description: "This is a template Figma file, turned into code using Anima. Learn more at AnimaApp.com",
            icon: "https://cdn.builder.io/api/v1/image/assets/TEMP/b169abf2ca800a06b3504099e3b053875fe18813c40840719db71443e9f452fa?placeholderIfAbsent=true&apiKey=b7978f95452b450fbd6d95e74f4b2ae2"
        }
    ];

    return (
        <div>
            <div className="relative bg-[#3E698B] p-20 ">
                <h3 className="text-white z-[2] text-5xl font-bold mb-4">Our Services</h3>
                <p className="text-white mb-8 w-[70%]">
                    Chez Shirkti, nous simplifions la création d'entreprises au Maroc en offrant des services complets :
                    génération de documents juridiques, recherche de centres de domiciliation, et suivi en temps réel.
                    Profitez d'une plateforme intuitive et sécurisée pour démarrer votre entreprise en toute sérénité.
                </p>
                <img className="star-motion " src="src/assets/shapes/star.svg" />

                <img src='src/assets/shapes/sphere.svg' className='sphere-motion' />


                {/* Cards Section */}
                <div className="flex flex-col  md:flex-row w-full gap-6 relative z-10">
                    <div className="flex flex-wrap gap-10 items-center self-start min-h-[425px] max-md:mt-10 max-md:mb-2.5 max-md:max-w-full">
                        {services.map((service, index) => (
                            <ServiceCard key={index} {...service} />
                        ))}
                    </div>
                

                    {/* Background Image */}
                    <div className="manMotion hidden md:block">
                        <img
                            src="/src/assets/motions/young.svg"
                            alt="Motion Young"
                            className="object-cover"
                        />
                    </div>
                </div>


            </div>

        </div>

    );
}

export default OurServices;