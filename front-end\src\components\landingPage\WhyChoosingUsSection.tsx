import CardWhyUs from "./CardWhyUs";



const WhyChoosingUsSection= ()=>{
    const bgMotion="https://cdn.builder.io/api/v1/image/assets/TEMP/0334feb5296d753b8b3dc85e66b88d9a6a378409a5c0a072460bad26ebaaf894?placeholderIfAbsent=true&apiKey=b7978f95452b450fbd6d95e74f4b2ae2";


    const features = [
        {
            title: "Simplification des démarches",
            description: "La plateforme vise à simplifier toutes les démarches légales et administratives liées à la création d'entreprises.",
            icon: "https://cdn.builder.io/api/v1/image/assets/TEMP/a8476f5dd769055d845884c43cad730b59fd07b220e35326e1289fdbd7c59b60?placeholderIfAbsent=true&apiKey=b7978f95452b450fbd6d95e74f4b2ae2"
        },
        {
            title: "Solution idéale pour les entrepreneurs",
            description: "\"Shirkti\" est la solution idéale pour ceux qui souhaitent démarrer leur activité rapidement et efficacement, en tirant parti des technologies modernes pour simplifier toutes les étapes nécessaires.",
            icon: "https://cdn.builder.io/api/v1/image/assets/TEMP/856c39a29bc82febf83170e5b6a5d66125379f723fcd36b62fc05bfe2f9f7013?placeholderIfAbsent=true&apiKey=b7978f95452b450fbd6d95e74f4b2ae2"
        },
        {
            title: "Interface utilisateur conviviale",
            description: "CHARIKTI se distingue par une interface utilisateur facile à utiliser, assurant une intégration complète avec les systèmes gouvernementaux pour garantir la confidentialité des données et la sécurité des transactions.",
            icon: "https://cdn.builder.io/api/v1/image/assets/TEMP/54832ac2cae31defe276990ede8b733ea211719298a9f4ba6a5a9959c5567ae2?placeholderIfAbsent=true&apiKey=b7978f95452b450fbd6d95e74f4b2ae2"
        },
        {
            title: "Recherche de centres de domiciliation",
            description: "La plateforme offre une solution intelligente pour rechercher des centres de domiciliation sans déplacement, ce qui permet de gagner du temps et des efforts.",
            icon: "/src/assets/icons/search.svg"
        }
    ];

    return(
        <div className={"bg-[#F3F3F3]"}>
        <div className="relative flex flex-col items-center justify-center p-10 max-w-full font-bold w-full max-md:mt-10">



            <div className="relative p-10 z-10 opacity-bg-0">
                <h2 className="self-start text-4xl leading-tight text-cyan-800 rotate-[1.7484555314694994e-7rad] tracking-[2.4px] max-md:max-w-full">
                    Why Choosing CHARIKTI
                </h2>
                <p className="mt-4 text-base w-[80%] tracking-tight leading-7 text-neutral-950 max-md:max-w-full">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation
                </p>
                {/* List of elements */}
                <div className="mt-12 w-full max-w-[1440] max-md:mt-10 max-md:max-w-full">
                    <div className="flex">
                        <div className="flex flex-row gap-5 flex-wrap">
                            {features.map((feature, index) => (
                                <div className="w-full max-w-[100%] md:max-w-[calc(50%-1.25rem)]" key={index}>
                                    <CardWhyUs {...feature} />
                                </div>
                            ))}
                        </div>
                        <div className="flex flex-row gap-5 flex-wrap justify-end items-end h-full">
                            <img src={bgMotion} alt={''} className="w-[100%] h-auto" />
                        </div>
                    </div>
                </div>

            </div>
        </div>
        </div>

    )
}

export default WhyChoosingUsSection;