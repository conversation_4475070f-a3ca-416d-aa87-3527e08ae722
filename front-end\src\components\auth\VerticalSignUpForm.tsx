import React, { useState } from 'react';
import { FaUser, FaEnvelope, FaLock } from 'react-icons/fa';
import AccountTypeStep from './signup-steps/AccountTypeStep';
import PersonalInfoStep from './signup-steps/PersonalInfoStep';
import IdentificationStep from './signup-steps/IdentificationStep';

interface VerticalSignUpFormProps {
  onSubmit: (formData: SignUpFormData) => void;
}

export interface SignUpFormData {
  accountType: 'personal' | 'enterprise' | 'courier' | '';
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
}

const VerticalSignUpForm: React.FC<VerticalSignUpFormProps> = ({ onSubmit }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<SignUpFormData>({
    accountType: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  });

  const steps = [
    { 
      number: 1, 
      title: 'Personal', 
      description: 'Select type of account',
      icon: <FaUser className="text-white" />
    },
    { 
      number: 2, 
      title: 'Contact', 
      description: 'Fill form to complete',
      icon: <FaEnvelope className="text-white" />
    },
    { 
      number: 3, 
      title: 'Security', 
      description: 'Set your credentials',
      icon: <FaLock className="text-white" />
    }
  ];

  const updateFormData = (data: Partial<SignUpFormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const goToNextStep = () => {
    setCurrentStep(prev => Math.min(prev + 1, steps.length));
  };

  const goToPreviousStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <AccountTypeStep 
            formData={formData} 
            updateFormData={updateFormData} 
            onNext={goToNextStep} 
          />
        );
      case 2:
        return (
          <PersonalInfoStep 
            formData={formData} 
            updateFormData={updateFormData} 
            onNext={goToNextStep} 
            onPrevious={goToPreviousStep} 
          />
        );
      case 3:
        return (
          <IdentificationStep 
            formData={formData} 
            updateFormData={updateFormData} 
            onSubmit={handleSubmit} 
            onPrevious={goToPreviousStep} 
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Left sidebar with steps */}
      <div className="w-64 bg-white p-8 shadow-md">
        <div className="mb-10">
          <h1 className="text-xl font-semibold text-indigo-600">.webdesign</h1>
        </div>
        
        <div className="space-y-8">
          {steps.map((step, index) => (
            <div key={step.number} className="flex items-start">
              <div 
                className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-4 ${
                  currentStep >= step.number 
                    ? 'bg-indigo-600' 
                    : 'bg-gray-300'
                }`}
              >
                {step.icon}
              </div>
              <div>
                <h3 className="font-medium">{step.title}</h3>
                <p className="text-sm text-gray-500">{step.description}</p>
              </div>
              {index < steps.length - 1 && (
                <div 
                  className={`absolute left-4 ml-4 h-12 w-0.5 -mt-2 ${
                    currentStep > step.number ? 'bg-indigo-600' : 'bg-gray-300'
                  }`}
                  style={{ marginTop: '2rem' }}
                ></div>
              )}
            </div>
          ))}
        </div>
      </div>
      
      {/* Main content */}
      <div className="flex-1 p-8">
        <div className="max-w-md mx-auto">
          {renderStep()}
        </div>
      </div>
    </div>
  );
};

export default VerticalSignUpForm;
