import { useQuery } from "@tanstack/react-query";
import apiClient from "../../services/api-client";
import { Order } from "../../models/order/Order";

const ORDERS_PATH = "/orders/users";

const useOrders = (UserId: number) =>
  useQuery<Order[], Error>({
    queryKey: ["orders"],
    queryFn: () =>
      apiClient
        .get<Order[]>(`${ORDERS_PATH}/${UserId}`)
        .then((response) => response.data),
  });

export default useOrders;
