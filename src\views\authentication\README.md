# Authentication Pages

This directory contains modern, responsive authentication pages with a consistent design theme.

## Design Features

All authentication pages share the following design characteristics:

- **Modern gradient background**: Purple gradient (135deg, #667eea 0%, #764ba2 100%)
- **Glass morphism effect**: Semi-transparent cards with backdrop blur
- **Decorative elements**: Floating geometric shapes and patterns
- **Responsive layout**: Two-column layout on desktop, single column on mobile
- **Consistent styling**: Rounded inputs, gradient buttons, and smooth animations

## Pages

### 1. Login (`/auth/login`)
- **File**: `Login.tsx`
- **Features**:
  - Email/password authentication
  - Remember me checkbox
  - Password visibility toggle
  - Link to forgot password
  - Link to registration

### 2. Register (`/auth/register`)
- **File**: `Register.tsx`
- **Features**:
  - Full name, email, password fields
  - Password confirmation
  - Role selection (Client, Coursier, Entreprise)
  - Password visibility toggles
  - Link to login

### 3. Forgot Password (`/auth/forgot-password`)
- **File**: `ForgotPassword.tsx`
- **Features**:
  - Email input for password reset
  - Success state with confirmation message
  - Link back to login

### 4. Error 404 (`/auth/404`)
- **File**: `Error.tsx`
- **Features**:
  - Large 404 display
  - Error message
  - Link back to home

## Design Inspiration

The design is inspired by the provided mockup with:
- Purple gradient background
- Welcome message on the left
- Login form on the right
- Modern glass morphism effects
- Floating decorative elements

## Technical Implementation

- **Framework**: React with TypeScript
- **UI Library**: Material-UI (MUI)
- **Icons**: Tabler Icons
- **Routing**: React Router
- **Styling**: MUI's sx prop with custom styles
- **Animations**: CSS keyframes for floating elements

## Usage

All pages are automatically loaded through the router configuration in `src/routes/Router.tsx`. They use the `BlankLayout` to provide a clean, full-screen experience without the dashboard navigation.

## Responsive Behavior

- **Desktop**: Two-column layout with welcome message and form
- **Tablet**: Adjusted spacing and font sizes
- **Mobile**: Single column layout with stacked content

## Accessibility

- Proper form labels and ARIA attributes
- Keyboard navigation support
- High contrast colors
- Screen reader friendly
