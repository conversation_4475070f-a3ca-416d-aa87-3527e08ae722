import { IconType } from "react-icons";
import ClientSection from "./ClientSection";

interface SectionProps {
  icon: IconType;
  label: string;
  link: string;
}

interface ClientSidebarProps {
  sections: SectionProps[];
  onLinkClick: (link: string) => void;
}

function ClientSidebar({ sections, onLinkClick }: ClientSidebarProps) {
  return (
    <div className="flex flex-col w-62 h-full pt-12 shadow-xl rounded-br-3xl bg-blue-100">
      {sections.map((section, index) => (
        <ClientSection
          key={index}
          icon={<section.icon />}
          label={section.label}
          link={section.link}
          onLinkClick={onLinkClick}
        />
      ))}
    </div>
  );
}

export default ClientSidebar;
