import React, { useState } from 'react';
import { IconType } from 'react-icons';

interface SidebarSection {
  icon: IconType;
  label: string;
  link: string;
}

interface AdminSidebarProps {
  sections: SidebarSection[];
  onLinkClick: (link: string) => void;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({ sections, onLinkClick }) => {
  const [activePage, setActivePage] = useState('/admin/dashboard');

  const handleClick = (link: string) => {
    setActivePage(link);
    onLinkClick(link);
  };

  return (
    <div className="flex flex-col w-64 h-full bg-[#ad83fe] rounded-br-[30px] text-white overflow-y-auto shadow-xl">
      <div className="p-6">
        <h2 className="text-lg font-medium">PAGES</h2>
      </div>
      <nav className="mt-2">
        <ul>
          {sections.map((section, index) => (
            <li key={index} className="mb-1">
              <button
                onClick={() => handleClick(section.link)}
                className={`flex items-center w-full px-6 py-3 text-left transition-colors duration-200 ${
                  activePage === section.link
                    ? 'bg-purple-700 text-white font-medium'
                    : 'text-white hover:bg-purple-600 hover:text-white'
                }`}
              >
                <section.icon className="mr-3 text-xl" />
                <span>{section.label}</span>
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default AdminSidebar;
