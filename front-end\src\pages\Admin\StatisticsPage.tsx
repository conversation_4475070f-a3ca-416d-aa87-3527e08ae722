import React, { useState, useEffect } from 'react';
import apiService from '../../services/apiService';
import InnerPageContainer from '../../components/InnerPageContainer';

const StatisticsPage = () => {
  const [stats, setStats] = useState({
    clientCount: 0,
    coursierCount: 0,
    entrepriseCount: 0,
    monthlyRegistrations: {
      labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
      clients: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      coursiers: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      entreprises: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    },
    entreprisesByActivity: {},
    coursiersByVehicle: {},
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);

        // Fetch all data
        const [clients, coursiers, entreprises] = await Promise.all([
          apiService.client.getAll(),
          apiService.coursier.getAll(),
          apiService.entreprise.getAll()
        ]);

        // Process monthly registrations (mock data for now)
        const monthlyData = {
          labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
          clients: [5, 8, 12, 15, 20, 22, 25, 30, 35, 40, 45, 50],
          coursiers: [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24],
          entreprises: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        };

        // Process entreprises by activity
        const entreprisesByActivity: Record<string, number> = {};
        entreprises.forEach(entreprise => {
          const activity = entreprise.activite;
          entreprisesByActivity[activity] = (entreprisesByActivity[activity] || 0) + 1;
        });

        // Process coursiers by vehicle type
        const coursiersByVehicle: Record<string, number> = {};
        coursiers.forEach(coursier => {
          const vehicleType = coursier.vehicle_type;
          coursiersByVehicle[vehicleType] = (coursiersByVehicle[vehicleType] || 0) + 1;
        });

        setStats({
          clientCount: clients.length,
          coursierCount: coursiers.length,
          entrepriseCount: entreprises.length,
          monthlyRegistrations: monthlyData,
          entreprisesByActivity,
          coursiersByVehicle,
        });

        setError(null);
      } catch (err) {
        console.error('Error fetching statistics:', err);
        setError('Erreur lors du chargement des statistiques');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  // No chart data needed for our simplified version

  return (
    <InnerPageContainer>
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-6">Statistiques</h1>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        ) : (
          <div className="space-y-8">
            {/* User Distribution */}
            <div className="bg-white p-4 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Répartition des utilisateurs</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="h-64 flex items-center justify-center">
                  <div className="w-full">
                    <div className="mb-4">
                      <div className="flex items-center mb-2">
                        <div className="w-4 h-4 bg-blue-500 mr-2"></div>
                        <span>Clients: {stats.clientCount}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-4">
                        <div
                          className="bg-blue-500 h-4 rounded-full"
                          style={{ width: `${(stats.clientCount / (stats.clientCount + stats.coursierCount + stats.entrepriseCount) * 100) || 0}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <div className="flex items-center mb-2">
                        <div className="w-4 h-4 bg-yellow-500 mr-2"></div>
                        <span>Coursiers: {stats.coursierCount}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-4">
                        <div
                          className="bg-yellow-500 h-4 rounded-full"
                          style={{ width: `${(stats.coursierCount / (stats.clientCount + stats.coursierCount + stats.entrepriseCount) * 100) || 0}%` }}
                        ></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center mb-2">
                        <div className="w-4 h-4 bg-green-500 mr-2"></div>
                        <span>Entreprises: {stats.entrepriseCount}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-4">
                        <div
                          className="bg-green-500 h-4 rounded-full"
                          style={{ width: `${(stats.entrepriseCount / (stats.clientCount + stats.coursierCount + stats.entrepriseCount) * 100) || 0}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="h-64 flex items-center justify-center">
                  <div className="flex flex-col items-center">
                    <div className="flex mb-4">
                      <div className="w-24 h-24 rounded-full border-8 border-blue-500 flex items-center justify-center text-blue-500 font-bold text-xl">
                        {stats.clientCount}
                      </div>
                      <div className="w-24 h-24 rounded-full border-8 border-yellow-500 flex items-center justify-center text-yellow-500 font-bold text-xl ml-4">
                        {stats.coursierCount}
                      </div>
                      <div className="w-24 h-24 rounded-full border-8 border-green-500 flex items-center justify-center text-green-500 font-bold text-xl ml-4">
                        {stats.entrepriseCount}
                      </div>
                    </div>
                    <div className="flex text-sm text-gray-600">
                      <div className="w-24 text-center">Clients</div>
                      <div className="w-24 text-center ml-4">Coursiers</div>
                      <div className="w-24 text-center ml-4">Entreprises</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Monthly Registrations */}
            <div className="bg-white p-4 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Inscriptions mensuelles</h2>
              <div className="h-64 flex items-center justify-center">
                <div className="w-full">
                  {stats.monthlyRegistrations.labels.map((month, index) => (
                    <div key={month} className="mb-4">
                      <div className="flex justify-between mb-1">
                        <span>{month}</span>
                        <span>
                          C: {stats.monthlyRegistrations.clients[index]} |
                          Co: {stats.monthlyRegistrations.coursiers[index]} |
                          E: {stats.monthlyRegistrations.entreprises[index]}
                        </span>
                      </div>
                      <div className="flex h-6">
                        <div
                          className="bg-blue-500 h-6"
                          style={{ width: `${(stats.monthlyRegistrations.clients[index] / 50) * 100}%` }}
                        ></div>
                        <div
                          className="bg-yellow-500 h-6"
                          style={{ width: `${(stats.monthlyRegistrations.coursiers[index] / 50) * 100}%` }}
                        ></div>
                        <div
                          className="bg-green-500 h-6"
                          style={{ width: `${(stats.monthlyRegistrations.entreprises[index] / 50) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Entreprises by Activity */}
            <div className="bg-white p-4 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Entreprises par activité</h2>
              <div className="h-64 overflow-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="py-2 px-4 text-left">Activité</th>
                      <th className="py-2 px-4 text-left">Nombre</th>
                      <th className="py-2 px-4 text-left">Pourcentage</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.entries(stats.entreprisesByActivity).map(([activity, count], index) => {
                      const total = Object.values(stats.entreprisesByActivity).reduce((a, b) => a + (b as number), 0);
                      const percentage = total > 0 ? ((count as number) / total) * 100 : 0;

                      return (
                        <tr key={activity} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="py-2 px-4">{activity}</td>
                          <td className="py-2 px-4">{count}</td>
                          <td className="py-2 px-4">
                            <div className="flex items-center">
                              <div className="w-32 bg-gray-200 rounded-full h-2.5 mr-2">
                                <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: `${percentage}%` }}></div>
                              </div>
                              <span>{percentage.toFixed(1)}%</span>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Coursiers by Vehicle Type */}
            <div className="bg-white p-4 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Coursiers par type de véhicule</h2>
              <div className="h-64 overflow-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="py-2 px-4 text-left">Type de véhicule</th>
                      <th className="py-2 px-4 text-left">Nombre</th>
                      <th className="py-2 px-4 text-left">Pourcentage</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.entries(stats.coursiersByVehicle).map(([vehicleType, count], index) => {
                      const total = Object.values(stats.coursiersByVehicle).reduce((a, b) => a + (b as number), 0);
                      const percentage = total > 0 ? ((count as number) / total) * 100 : 0;

                      return (
                        <tr key={vehicleType} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="py-2 px-4">{vehicleType}</td>
                          <td className="py-2 px-4">{count}</td>
                          <td className="py-2 px-4">
                            <div className="flex items-center">
                              <div className="w-32 bg-gray-200 rounded-full h-2.5 mr-2">
                                <div className="bg-yellow-500 h-2.5 rounded-full" style={{ width: `${percentage}%` }}></div>
                              </div>
                              <span>{percentage.toFixed(1)}%</span>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </InnerPageContainer>
  );
};

export default StatisticsPage;
