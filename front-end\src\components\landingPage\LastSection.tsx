interface listItem{
    label: string;
    link: string;
};

interface footerList{
    title: string;
    items: listItem[];
}

const lists: footerList[] = [
{
    title: 'About Us',
    items:[
        {'label': 'Terms of use','link': '#Terms'},
        {'label': 'Privacy policy','link': '#PrivacyPolicy'},
        {'label': 'Cookie policy','link': "#CookiePolicy"},
    ]

},
{
    title: 'Notre Services',
    items: [
        {'label':'Juridique entreprise','link': '#Terms'},
        {'label': 'Domiciliation', 'link': '#Terms'},
        {'label':'Accompagnement', 'link': '#Terms'},
    ]
},
{
    title: 'Les Partenaires',
    items: [
        {'label':'Branding', 'link': '#Terms'},
        {'label': 'Design', 'link': '#Terms'},
        {'label': 'Marketing', 'link': '#Terms'},
    ]
},

];

const LastSection=()=>{




    return (
        <div>
            <footer className="footer bg-white border text-black p-10">
                <aside>
                    <img src={'/src/assets/motions/logoLastSection.svg'} />

                    <q className="text-2xl">CHARIKTI - Votre partenaire de confiance</q>

                </aside>


                {lists.map((list)=>{
                    return (
                        <nav key={list.title}>
                            <h6 className="footer-title text-[#5B99C2]">{list.title}</h6>
                            {list.items.map((item, index)=>{
                                return <a key={index} href={item.link} className="link link-hover">{item.label}</a>
                            })}
                        </nav>
                    )
                })}


            </footer>
        </div>
    );
}

export default LastSection;