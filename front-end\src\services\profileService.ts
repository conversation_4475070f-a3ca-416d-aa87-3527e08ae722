import api from './api';
import { handleApiError } from './errorHandler';
import apiService from './apiService';

// Types pour les différents profils
export interface ClientProfile {
  id?: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  address: string;
  companyName?: string;
  profileImage?: string;
}

export interface CoursierProfile {
  id?: number;
  name: string;
  email: string;
  phoneNumber: string;
  address: string;
  vehicleType: string;
  licenseNumber: string;
  profileImage?: string;
}

export interface EntrepriseProfile {
  id?: number;
  denomination: string;
  email: string;
  phoneNumber: string;
  siegeSocial: string;
  formeJuridique: string;
  activite: string;
  nRC: string;
  nCnss: string;
  iFiscale: string;
  taxProfessionel: string;
  ice?: string;
  profileImage?: string;
  // Informations du gérant
  gerantName?: string;
  gerantCin?: string;
  gerantAddress?: string;
}

export interface AdminProfile {
  id?: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  role: string;
  permissions?: string[];
  lastLogin?: string;
  createdAt?: string;
  profileImage?: string;
}

// Service pour les profils client
export const clientProfileService = {
  // Récupérer le profil client
  getProfile: async (): Promise<ClientProfile> => {
    try {
      // Essayer d'abord avec l'API Laravel
      try {
        const response = await fetch('http://localhost:8000/api/clients/profile', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Client profile from Laravel API:', data);

          // Convertir le format de l'API Laravel au format attendu par l'application
          return {
            id: data.id,
            firstName: data.first_name,
            lastName: data.last_name,
            email: data.email,
            phoneNumber: data.phone_number,
            address: data.address,
            companyName: data.company_name,
            profileImage: data.profile_image
          };
        }
      } catch (apiError) {
        console.error('Error fetching from Laravel API:', apiError);
      }

      // Fallback à l'API mock
      const response = await api.get<ClientProfile>('/client/profile');
      return response.data;
    } catch (error) {
      return handleApiError(error, 'Erreur lors de la récupération du profil client');
    }
  },

  // Mettre à jour le profil client
  updateProfile: async (profile: ClientProfile): Promise<ClientProfile> => {
    try {
      const response = await api.put<ClientProfile>('/client/profile', profile);
      return response.data;
    } catch (error) {
      return handleApiError(error, 'Erreur lors de la mise à jour du profil client');
    }
  },

  // Télécharger une image de profil
  uploadProfileImage: async (file: File): Promise<string> => {
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await api.post<{ imageUrl: string }>('/client/profile/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data.imageUrl;
    } catch (error) {
      return handleApiError(error, 'Erreur lors du téléchargement de l\'image de profil');
    }
  },
};

// Service pour les profils coursier
export const coursierProfileService = {
  // Récupérer le profil coursier
  getProfile: async (): Promise<CoursierProfile> => {
    try {
      // Essayer d'abord avec l'API Laravel
      try {
        const response = await fetch('http://localhost:8000/api/coursiers/profile', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Coursier profile from Laravel API:', data);

          // Convertir le format de l'API Laravel au format attendu par l'application
          return {
            id: data.id,
            name: data.name,
            email: data.email,
            phoneNumber: data.phone_number,
            address: data.address,
            vehicleType: data.vehicle_type,
            licenseNumber: data.license_number,
            profileImage: data.profile_image
          };
        }
      } catch (apiError) {
        console.error('Error fetching from Laravel API:', apiError);
      }

      // Fallback à l'API mock
      const response = await api.get<CoursierProfile>('/coursier/profile');
      return response.data;
    } catch (error) {
      return handleApiError(error, 'Erreur lors de la récupération du profil coursier');
    }
  },

  // Mettre à jour le profil coursier
  updateProfile: async (profile: CoursierProfile): Promise<CoursierProfile> => {
    try {
      const response = await api.put<CoursierProfile>('/coursier/profile', profile);
      return response.data;
    } catch (error) {
      return handleApiError(error, 'Erreur lors de la mise à jour du profil coursier');
    }
  },

  // Télécharger une image de profil
  uploadProfileImage: async (file: File): Promise<string> => {
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await api.post<{ imageUrl: string }>('/coursier/profile/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data.imageUrl;
    } catch (error) {
      return handleApiError(error, 'Erreur lors du téléchargement de l\'image de profil');
    }
  },
};

// Service pour les profils entreprise
export const entrepriseProfileService = {
  // Récupérer le profil entreprise
  getProfile: async (): Promise<EntrepriseProfile> => {
    try {
      // Essayer d'abord avec l'API Laravel
      try {
        const response = await fetch('http://localhost:8000/api/entreprises/profile', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Entreprise profile from Laravel API:', data);

          // Convertir le format de l'API Laravel au format attendu par l'application
          return {
            id: data.id,
            denomination: data.denomination,
            email: data.email,
            phoneNumber: data.phone_number,
            siegeSocial: data.siege_social,
            formeJuridique: data.forme_juridique,
            activite: data.activite,
            nRC: data.n_rc,
            nCnss: data.n_cnss,
            iFiscale: data.i_fiscale,
            taxProfessionel: data.tax_professionel,
            ice: data.ice,
            profileImage: data.profile_image,
            // Informations du gérant
            gerantName: data.gerant_name,
            gerantCin: data.gerant_cin,
            gerantAddress: data.gerant_address
          };
        }
      } catch (apiError) {
        console.error('Error fetching from Laravel API:', apiError);
      }

      // Fallback à l'API mock
      const response = await api.get<EntrepriseProfile>('/entreprise/profile');
      return response.data;
    } catch (error) {
      return handleApiError(error, 'Erreur lors de la récupération du profil entreprise');
    }
  },

  // Mettre à jour le profil entreprise
  updateProfile: async (profile: EntrepriseProfile): Promise<EntrepriseProfile> => {
    try {
      // Essayer d'abord avec l'API Laravel
      try {
        const response = await fetch('http://localhost:8000/api/entreprises/profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            denomination: profile.denomination,
            email: profile.email,
            phone_number: profile.phoneNumber,
            siege_social: profile.siegeSocial,
            forme_juridique: profile.formeJuridique,
            activite: profile.activite,
            n_rc: profile.nRC,
            n_cnss: profile.nCnss,
            i_fiscale: profile.iFiscale,
            tax_professionel: profile.taxProfessionel,
            ice: profile.ice,
            // Informations du gérant
            gerant_name: profile.gerantName,
            gerant_cin: profile.gerantCin,
            gerant_address: profile.gerantAddress
          })
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Entreprise profile updated via Laravel API:', data);
          return profile;
        }
      } catch (apiError) {
        console.error('Error updating via Laravel API:', apiError);
      }

      // Fallback à l'API mock
      const response = await api.put<EntrepriseProfile>('/entreprise/profile', profile);
      return response.data;
    } catch (error) {
      return handleApiError(error, 'Erreur lors de la mise à jour du profil entreprise');
    }
  },

  // Télécharger une image de profil
  uploadProfileImage: async (file: File): Promise<string> => {
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await api.post<{ imageUrl: string }>('/entreprise/profile/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data.imageUrl;
    } catch (error) {
      return handleApiError(error, 'Erreur lors du téléchargement de l\'image de profil');
    }
  },
};

// Service pour les profils administrateur
export const adminProfileService = {
  // Récupérer le profil administrateur
  getProfile: async (): Promise<AdminProfile> => {
    try {
      // Essayer d'abord avec l'API Laravel
      try {
        const response = await fetch('http://localhost:8000/api/admins/profile', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Admin profile from Laravel API:', data);

          // Convertir le format de l'API Laravel au format attendu par l'application
          return {
            id: data.id,
            firstName: data.first_name,
            lastName: data.last_name,
            email: data.email,
            phoneNumber: data.phone_number,
            role: data.role,
            permissions: data.permissions,
            lastLogin: data.last_login,
            createdAt: data.created_at,
            profileImage: data.profile_image
          };
        }
      } catch (apiError) {
        console.error('Error fetching from Laravel API:', apiError);
      }

      // Fallback à l'API mock - données par défaut pour l'administrateur
      return {
        id: 1,
        firstName: 'Admin',
        lastName: 'System',
        email: '<EMAIL>',
        phoneNumber: '+212 600000000',
        role: 'admin',
        permissions: ['manage_users', 'manage_content', 'view_statistics', 'system_settings'],
        lastLogin: new Date().toISOString(),
        createdAt: '2023-01-01T00:00:00.000Z',
        profileImage: 'https://randomuser.me/api/portraits/men/10.jpg'
      };
    } catch (error) {
      return handleApiError(error, 'Erreur lors de la récupération du profil administrateur');
    }
  },

  // Mettre à jour le profil administrateur
  updateProfile: async (profile: AdminProfile): Promise<AdminProfile> => {
    try {
      // Essayer d'abord avec l'API Laravel
      try {
        const response = await fetch('http://localhost:8000/api/admins/profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            first_name: profile.firstName,
            last_name: profile.lastName,
            email: profile.email,
            phone_number: profile.phoneNumber,
            role: profile.role,
            permissions: profile.permissions
          })
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Admin profile updated via Laravel API:', data);
          return profile;
        }
      } catch (apiError) {
        console.error('Error updating via Laravel API:', apiError);
      }

      // Fallback - simuler une mise à jour réussie
      console.log('Admin profile updated (mock):', profile);
      return profile;
    } catch (error) {
      return handleApiError(error, 'Erreur lors de la mise à jour du profil administrateur');
    }
  },

  // Télécharger une image de profil
  uploadProfileImage: async (file: File): Promise<string> => {
    try {
      const formData = new FormData();
      formData.append('image', file);

      // Essayer d'abord avec l'API Laravel
      try {
        const response = await fetch('http://localhost:8000/api/admins/profile/image', {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: formData
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Admin profile image uploaded via Laravel API:', data);
          return data.image_url;
        }
      } catch (apiError) {
        console.error('Error uploading image via Laravel API:', apiError);
      }

      // Fallback - créer une URL temporaire pour l'image
      const imageUrl = URL.createObjectURL(file);

      // Stocker l'image dans le localStorage
      const reader = new FileReader();
      reader.onloadend = () => {
        localStorage.setItem('admin_profile_image', reader.result as string);
      };
      reader.readAsDataURL(file);

      return imageUrl;
    } catch (error) {
      return handleApiError(error, 'Erreur lors du téléchargement de l\'image de profil');
    }
  },
};
