import { useState } from "react";
import { FaMoon, FaSun } from "react-icons/fa";

const ThemeSwitcher = () => {
  const [theme, setTheme] = useState("light");

  const handleThemeChange = () => {
    const newTheme = theme === "light" ? "dark" : "light";
    setTheme(newTheme);
    document.documentElement.setAttribute("data-theme", newTheme);
  };

  return (
    <button
      className="p-1 rounded-full text-gray-500 hover:bg-gray-100"
      onClick={handleThemeChange}
      aria-label="Toggle theme"
    >
      {theme === "light" ? <FaMoon className="h-5 w-5" /> : <FaSun className="h-5 w-5 text-yellow-500" />}
    </button>
  );
};

export default ThemeSwitcher;
