import React, { useState, useEffect } from 'react';
import { Md<PERSON>erson, MdDeliveryDining, MdBusiness } from 'react-icons/md';
import apiService from '../../services/apiService';

const AdminDashboardHome = () => {
  const [stats, setStats] = useState({
    clientCount: 0,
    coursierCount: 0,
    entrepriseCount: 0,
    recentClients: [],
    recentCoursiers: [],
    recentEntreprises: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);

        // Fetch counts
        const [clients, coursiers, entreprises] = await Promise.all([
          apiService.client.getAll(),
          apiService.coursier.getAll(),
          apiService.entreprise.getAll()
        ]);

        setStats({
          clientCount: clients.length,
          coursierCount: coursiers.length,
          entrepriseCount: entreprises.length,
          recentClients: clients.slice(0, 5),
          recentCoursiers: coursiers.slice(0, 5),
          recentEntreprises: entreprises.slice(0, 5)
        });

        setError(null);
      } catch (err) {
        console.error('Error fetching stats:', err);
        setError('Erreur lors du chargement des statistiques');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  // No chart data needed for our simplified version

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-6">Tableau de bord administrateur</h1>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      ) : (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-blue-100 p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <MdPerson className="text-4xl text-blue-500 mr-4" />
                <div>
                  <h2 className="text-xl font-semibold">Clients</h2>
                  <p className="text-3xl font-bold">{stats.clientCount}</p>
                </div>
              </div>
            </div>

            <div className="bg-yellow-100 p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <MdDeliveryDining className="text-4xl text-yellow-500 mr-4" />
                <div>
                  <h2 className="text-xl font-semibold">Coursiers</h2>
                  <p className="text-3xl font-bold">{stats.coursierCount}</p>
                </div>
              </div>
            </div>

            <div className="bg-green-100 p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <MdBusiness className="text-4xl text-green-500 mr-4" />
                <div>
                  <h2 className="text-xl font-semibold">Entreprises</h2>
                  <p className="text-3xl font-bold">{stats.entrepriseCount}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Services Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-purple-100 p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="text-4xl text-purple-500 mr-4">🏢</div>
                <div>
                  <h2 className="text-xl font-semibold">Domiciliations</h2>
                  <p className="text-3xl font-bold">24</p>
                </div>
              </div>
            </div>

            <div className="bg-indigo-100 p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="text-4xl text-indigo-500 mr-4">⚖️</div>
                <div>
                  <h2 className="text-xl font-semibold">Juridique</h2>
                  <p className="text-3xl font-bold">18</p>
                </div>
              </div>
            </div>

            <div className="bg-teal-100 p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="text-4xl text-teal-500 mr-4">📊</div>
                <div>
                  <h2 className="text-xl font-semibold">Comptabilité</h2>
                  <p className="text-3xl font-bold">32</p>
                </div>
              </div>
            </div>
          </div>

          {/* New Charts based on the image */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Répartition des utilisateurs</h2>
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <div className="w-4 h-4 bg-blue-500 mr-2"></div>
                  <span>Clients: 1</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
                  <div
                    className="bg-blue-500 h-4 rounded-full"
                    style={{ width: '30%' }}
                  ></div>
                </div>

                <div className="flex items-center mb-2">
                  <div className="w-4 h-4 bg-yellow-500 mr-2"></div>
                  <span>Coursiers: 1</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
                  <div
                    className="bg-yellow-500 h-4 rounded-full"
                    style={{ width: '30%' }}
                  ></div>
                </div>

                <div className="flex items-center mb-2">
                  <div className="w-4 h-4 bg-green-500 mr-2"></div>
                  <span>Entreprises: 1</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-4">
                  <div
                    className="bg-green-500 h-4 rounded-full"
                    style={{ width: '30%' }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Répartition des services</h2>
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <div className="w-4 h-4 bg-blue-500 mr-2"></div>
                  <span>Juridique : 10</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
                  <div
                    className="bg-blue-500 h-4 rounded-full"
                    style={{ width: '50%' }}
                  ></div>
                </div>

                <div className="flex items-center mb-2">
                  <div className="w-4 h-4 bg-yellow-500 mr-2"></div>
                  <span>Domiciliations : 6</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
                  <div
                    className="bg-yellow-500 h-4 rounded-full"
                    style={{ width: '30%' }}
                  ></div>
                </div>

                <div className="flex items-center mb-2">
                  <div className="w-4 h-4 bg-green-500 mr-2"></div>
                  <span>Comptabilité : 4</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-4">
                  <div
                    className="bg-green-500 h-4 rounded-full"
                    style={{ width: '20%' }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          {/* Domiciliations Table */}
          <div className="bg-white p-4 rounded-lg shadow-md mb-8">
            <h2 className="text-xl font-semibold mb-4">Services de Domiciliation</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Type de service
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Prestataire de service
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Coût de service
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Statut
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-purple-100 text-purple-800 text-xs font-semibold px-2 py-1 rounded">
                        Domiciliation Standard
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">Centre d'Affaires Casablanca</td>
                    <td className="py-2 px-4 border-b border-gray-200">1500 DH / an</td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded">
                        Actif
                      </span>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-purple-100 text-purple-800 text-xs font-semibold px-2 py-1 rounded">
                        Domiciliation Premium
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">Business Center Rabat</td>
                    <td className="py-2 px-4 border-b border-gray-200">2500 DH / an</td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded">
                        Actif
                      </span>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-purple-100 text-purple-800 text-xs font-semibold px-2 py-1 rounded">
                        Domiciliation Virtuelle
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">Virtual Office Marrakech</td>
                    <td className="py-2 px-4 border-b border-gray-200">1200 DH / an</td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-yellow-100 text-yellow-800 text-xs font-semibold px-2 py-1 rounded">
                        En attente
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Juridique Table */}
          <div className="bg-white p-4 rounded-lg shadow-md mb-8">
            <h2 className="text-xl font-semibold mb-4">Services Juridiques</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Type de service
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Prestataire de service
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Coût de service
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Statut
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-2 py-1 rounded">
                        Création de société
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">Cabinet Juridique Hassan</td>
                    <td className="py-2 px-4 border-b border-gray-200">5000 DH</td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded">
                        Terminé
                      </span>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-2 py-1 rounded">
                        Transfert de siège social
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">Avocats & Associés</td>
                    <td className="py-2 px-4 border-b border-gray-200">3500 DH</td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
                        En cours
                      </span>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-2 py-1 rounded">
                        Augmentation de capital
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">LegalTech Maroc</td>
                    <td className="py-2 px-4 border-b border-gray-200">4200 DH</td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-yellow-100 text-yellow-800 text-xs font-semibold px-2 py-1 rounded">
                        En attente
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Comptabilité Table */}
          <div className="bg-white p-4 rounded-lg shadow-md mb-8">
            <h2 className="text-xl font-semibold mb-4">Services de Comptabilité</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Type de service
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Prestataire de service
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Coût de service
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Statut
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-teal-100 text-teal-800 text-xs font-semibold px-2 py-1 rounded">
                        Tenue comptable mensuelle
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">Fiduciaire Atlas</td>
                    <td className="py-2 px-4 border-b border-gray-200">800 DH / mois</td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded">
                        Actif
                      </span>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-teal-100 text-teal-800 text-xs font-semibold px-2 py-1 rounded">
                        Bilan annuel
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">Comptables Associés</td>
                    <td className="py-2 px-4 border-b border-gray-200">3000 DH</td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
                        En cours
                      </span>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-teal-100 text-teal-800 text-xs font-semibold px-2 py-1 rounded">
                        Déclaration fiscale
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">Expert Comptable Maroc</td>
                    <td className="py-2 px-4 border-b border-gray-200">1200 DH</td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded">
                        Terminé
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Recent Users */}
          <div className="bg-white p-4 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Nouveaux Partenaires</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white">
                <thead>
                  <tr>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Nom
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Email
                    </th>
                    <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Téléphone
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {/* This would be populated with actual data */}
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
                        Client
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">John Doe</td>
                    <td className="py-2 px-4 border-b border-gray-200"><EMAIL></td>
                    <td className="py-2 px-4 border-b border-gray-200">+212 612345678</td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-yellow-100 text-yellow-800 text-xs font-semibold px-2 py-1 rounded">
                        Coursier
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">Ahmed Alami</td>
                    <td className="py-2 px-4 border-b border-gray-200"><EMAIL></td>
                    <td className="py-2 px-4 border-b border-gray-200">+212 623456789</td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b border-gray-200">
                      <span className="bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded">
                        Entreprise
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">Charikti SARL</td>
                    <td className="py-2 px-4 border-b border-gray-200"><EMAIL></td>
                    <td className="py-2 px-4 border-b border-gray-200">+212 522123456</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default AdminDashboardHome;
