import React, { useState, useEffect } from 'react';
import { MdClose } from 'react-icons/md';

interface Entreprise {
  id: number;
  denomination: string;
  email: string;
  phone_number: string;
  siege_social: string;
  forme_juridique: string;
  activite: string;
  n_rc: string;
  n_cnss: string;
  i_fiscale: string;
  tax_professionel: string;
  ice?: string;
  profile_image?: string;
}

interface EntrepriseModalProps {
  entreprise: Entreprise | null;
  onClose: () => void;
  onSave: (entrepriseData: Partial<Entreprise>) => void;
}

const formeJuridique = [
  "SARL",
  "SARL AU",
  "SA",
  "SNC",
  "SCS",
  "SCA",
  "GIE",
  "Auto entrepreneur",
  "Autre"
];

const activites = [
  "Commerce",
  "Services",
  "Industrie",
  "Artisanat",
  "Agriculture",
  "Comptable",
  "Expert comptable",
  "Centre d'affaire",
  "Autre"
];

const EntrepriseModal: React.FC<EntrepriseModalProps> = ({ entreprise, onClose, onSave }) => {
  const [formData, setFormData] = useState<Partial<Entreprise>>({
    denomination: '',
    email: '',
    phone_number: '',
    siege_social: '',
    forme_juridique: 'SARL',
    activite: 'Commerce',
    n_rc: '',
    n_cnss: '',
    i_fiscale: '',
    tax_professionel: '',
    ice: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  useEffect(() => {
    if (entreprise) {
      setFormData({
        denomination: entreprise.denomination,
        email: entreprise.email,
        phone_number: entreprise.phone_number,
        siege_social: entreprise.siege_social,
        forme_juridique: entreprise.forme_juridique,
        activite: entreprise.activite,
        n_rc: entreprise.n_rc,
        n_cnss: entreprise.n_cnss,
        i_fiscale: entreprise.i_fiscale,
        tax_professionel: entreprise.tax_professionel,
        ice: entreprise.ice,
      });

      if (entreprise.profile_image) {
        setImagePreview(`http://localhost:8000/storage/${entreprise.profile_image}`);
      }
    }
  }, [entreprise]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setProfileImage(file);

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.denomination?.trim()) {
      newErrors.denomination = 'La dénomination est requise';
    }

    if (!formData.email?.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'L\'email n\'est pas valide';
    }

    if (!formData.phone_number?.trim()) {
      newErrors.phone_number = 'Le numéro de téléphone est requis';
    }

    if (!formData.siege_social?.trim()) {
      newErrors.siege_social = 'Le siège social est requis';
    }

    if (!formData.n_rc?.trim()) {
      newErrors.n_rc = 'Le numéro RC est requis';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Handle image upload separately if needed
    // For now, just pass the form data
    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">
            {entreprise ? 'Modifier l\'entreprise' : 'Ajouter une entreprise'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <MdClose className="text-2xl" />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {/* Profile Image */}
            <div className="md:col-span-2 flex flex-col items-center mb-4">
              <div className="w-32 h-32 rounded-full overflow-hidden bg-gray-100 mb-2">
                {imagePreview ? (
                  <img
                    src={imagePreview}
                    alt="Profile Preview"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-green-100 text-green-500 text-2xl font-bold">
                    {formData.denomination?.charAt(0) || ''}
                  </div>
                )}
              </div>
              <label className="cursor-pointer bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                Choisir une image
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageChange}
                />
              </label>
            </div>

            {/* Denomination */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Dénomination
              </label>
              <input
                type="text"
                name="denomination"
                value={formData.denomination}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-lg ${
                  errors.denomination ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.denomination && (
                <p className="text-red-500 text-xs mt-1">{errors.denomination}</p>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-lg ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>

            {/* Phone Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Téléphone
              </label>
              <input
                type="text"
                name="phone_number"
                value={formData.phone_number}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-lg ${
                  errors.phone_number ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.phone_number && (
                <p className="text-red-500 text-xs mt-1">{errors.phone_number}</p>
              )}
            </div>

            {/* Siege Social */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Siège social
              </label>
              <input
                type="text"
                name="siege_social"
                value={formData.siege_social}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-lg ${
                  errors.siege_social ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.siege_social && (
                <p className="text-red-500 text-xs mt-1">{errors.siege_social}</p>
              )}
            </div>

            {/* Forme Juridique */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Forme juridique
              </label>
              <select
                name="forme_juridique"
                value={formData.forme_juridique}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              >
                {formeJuridique.map((forme) => (
                  <option key={forme} value={forme}>
                    {forme}
                  </option>
                ))}
              </select>
            </div>

            {/* Activite */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Activité
              </label>
              <select
                name="activite"
                value={formData.activite}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              >
                {activites.map((activite) => (
                  <option key={activite} value={activite}>
                    {activite}
                  </option>
                ))}
              </select>
            </div>

            {/* N RC */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                N° RC
              </label>
              <input
                type="text"
                name="n_rc"
                value={formData.n_rc}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-lg ${
                  errors.n_rc ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.n_rc && (
                <p className="text-red-500 text-xs mt-1">{errors.n_rc}</p>
              )}
            </div>

            {/* N CNSS */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                N° CNSS
              </label>
              <input
                type="text"
                name="n_cnss"
                value={formData.n_cnss}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              />
            </div>

            {/* I Fiscale */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Identifiant fiscal
              </label>
              <input
                type="text"
                name="i_fiscale"
                value={formData.i_fiscale}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              />
            </div>

            {/* Tax Professionel */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Taxe professionnelle
              </label>
              <input
                type="text"
                name="tax_professionel"
                value={formData.tax_professionel}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              />
            </div>

            {/* ICE */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                ICE
              </label>
              <input
                type="text"
                name="ice"
                value={formData.ice}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border rounded-lg hover:bg-gray-100"
            >
              Annuler
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
            >
              {entreprise ? 'Mettre à jour' : 'Ajouter'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EntrepriseModal;
