import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import PaginationButton from "./PaginationButton";


interface Props {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
}



const Pagination = ({ currentPage, totalPages, onPageChange }: Props) => {


  return (
    <div className="flex items-center justify-end space-x-2 mt-6">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        className="px-3 py-2 text-gray-600 hover:text-blue-500 flex items-center transition-colors duration-200 disabled:opacity-50"
        disabled={currentPage === 1}
      >
        <FaChevronLeft className="w-4 h-4 mr-1" />
        Previous
      </button>

      {[...Array(totalPages)].map((_, index) => (
        <PaginationButton
          key={index + 1}
          active={currentPage === index + 1}
          onClick={() => onPageChange(index + 1)}
        >
          {index + 1}
        </PaginationButton>
      ))}

      <button
        onClick={() => onPageChange(currentPage + 1)}
        className="px-3 py-2 text-gray-600 hover:text-blue-500 flex items-center transition-colors duration-200 disabled:opacity-50"
        disabled={currentPage === totalPages}
      >
        Next
        <FaChevronRight className="w-4 h-4 ml-1" />
      </button>
    </div>
  );
};


export default Pagination;