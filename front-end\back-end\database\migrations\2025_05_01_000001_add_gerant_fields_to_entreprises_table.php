<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('entreprises', function (Blueprint $table) {
            $table->string('gerant_name')->nullable()->after('user_id');
            $table->string('gerant_cin')->nullable()->after('gerant_name');
            $table->string('gerant_address')->nullable()->after('gerant_cin');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('entreprises', function (Blueprint $table) {
            $table->dropColumn(['gerant_name', 'gerant_cin', 'gerant_address']);
        });
    }
};
