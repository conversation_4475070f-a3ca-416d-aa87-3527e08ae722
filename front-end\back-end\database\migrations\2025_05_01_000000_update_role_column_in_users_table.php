<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Vérifier le type actuel de la colonne 'role'
        $column = DB::select("SHOW COLUMNS FROM users LIKE 'role'")[0];
        
        // Si c'est un ENUM, modifier pour inclure 'admin'
        if (strpos($column->Type, 'enum') !== false) {
            // Extraire les valeurs actuelles de l'enum
            preg_match("/^enum\((.*)\)$/", $column->Type, $matches);
            $values = str_getcsv($matches[1], ',', "'");
            
            // Vérifier si 'admin' est déjà présent
            if (!in_array('admin', $values)) {
                // Ajouter 'admin' aux valeurs
                $values[] = 'admin';
                
                // Construire la nouvelle définition ENUM
                $newEnum = "enum('" . implode("','", $values) . "')";
                
                // Modifier la colonne
                DB::statement("ALTER TABLE users MODIFY COLUMN role $newEnum");
            }
        } else {
            // Si ce n'est pas un ENUM, modifier en VARCHAR pour plus de flexibilité
            Schema::table('users', function (Blueprint $table) {
                $table->string('role', 20)->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revenir à l'ENUM original sans 'admin'
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('client', 'coursier', 'entreprise')");
    }
};
