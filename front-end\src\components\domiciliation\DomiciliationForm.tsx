import React, { useState } from 'react';
import { IoArrowBack } from 'react-icons/io5';
import InformationStep from './InformationStep';

interface DomiciliationFormProps {
  onClose: () => void;
}

export interface DomiciliationFormData {
  // Informations du gérant
  nomCompletGerant: string;
  cinGerant: string;
  adresseGerant: string;
  // Informations entreprise
  nomEntreprise: string;
  ice: string;
  // Durée de domiciliation
  dateDebut: string;
  dateFin: string;
}

const DomiciliationForm: React.FC<DomiciliationFormProps> = ({ onClose }) => {
  const [formData, setFormData] = useState<DomiciliationFormData>({
    nomCompletGerant: '',
    cinGerant: '',
    adresseGerant: '',
    nomEntreprise: '',
    ice: '',
    dateDebut: '',
    dateFin: '',
  });

  const handleSubmit = () => {
    // Ici, vous pouvez envoyer les données du formulaire à votre API
    console.log('Formulaire soumis avec les données:', formData);
    // Fermer le formulaire ou rediriger l'utilisateur
    onClose();
  };

  const updateFormData = (data: Partial<DomiciliationFormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-50 rounded-lg shadow-md border border-gray-200 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-4">
          <div className="flex items-center mb-6">
            <button
              onClick={onClose}
              className="text-blue-600 hover:text-blue-700 mr-4"
            >
              <IoArrowBack size={24} />
            </button>
            <h2 className="text-xl font-semibold text-gray-800">Création de domiciliation</h2>
          </div>

          <InformationStep
            formData={formData}
            updateFormData={updateFormData}
            onNext={handleSubmit}
          />
        </div>
      </div>
    </div>
  );
};

export default DomiciliationForm;
