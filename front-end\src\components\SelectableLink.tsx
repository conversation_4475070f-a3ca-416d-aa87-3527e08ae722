import React, { useState } from "react";
import { Link } from "react-router-dom";

export const SelectableLink = ({ id, children }) => {
    const [isChecked, setIsChecked] = useState(false);

    const handleClick = () => {
        setIsChecked(prev => !prev); // Toggle the checked state
        // You can also pass the ID to your data handler here
        console.log(`Link ID: ${id}`);
    };

    return (
        <Link
            to="#"
            onClick={handleClick}
            className={`btn text-white px-4 py-2 rounded-lg transition-colors duration-300 
                ${isChecked ? 'bg-green-500' : 'bg-gray-500'}`}
        >
            {children}
        </Link>
    );
};