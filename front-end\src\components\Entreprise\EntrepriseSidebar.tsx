import { IconType } from "react-icons";
import EntrepriseSection from "./EntrepriseSection";

interface SectionProps {
  icon: IconType;
  label: string;
  link: string;
}

interface EntrepriseSidebarProps {
  sections: SectionProps[];
  onLinkClick: (link: string) => void;
}

function EntrepriseSidebar({ sections, onLinkClick }: EntrepriseSidebarProps) {
  return (
    <div className="flex flex-col w-62 h-full pt-12 shadow-xl rounded-br-3xl bg-blue-100">
      {sections.map((section, index) => (
        <EntrepriseSection
          key={index}
          icon={<section.icon />}
          label={section.label}
          link={section.link}
          onLinkClick={onLinkClick}
        />
      ))}
    </div>
  );
}

export default EntrepriseSidebar;
