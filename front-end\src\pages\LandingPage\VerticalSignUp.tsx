import React from 'react';
import { useNavigate } from 'react-router-dom';
import VerticalSignUpForm, { SignUpFormData } from '../../components/auth/VerticalSignUpForm';

const VerticalSignUp: React.FC = () => {
  const navigate = useNavigate();

  const handleSignUp = (formData: SignUpFormData) => {
    console.log('Form submitted with data:', formData);
    
    // Here you would typically send the data to your backend API
    // For now, we'll just simulate a successful signup and redirect to login
    
    // Redirect to login page after successful signup
    setTimeout(() => {
      navigate('/signin');
    }, 1000);
  };

  return (
    <VerticalSignUpForm onSubmit={handleSignUp} />
  );
};

export default VerticalSignUp;
