import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import apiService from './apiService';

// Définir l'URL de base de l'API
const API_URL = 'http://localhost:8000/api';

// Variable pour activer/désactiver le mode mock
const USE_MOCK = false; // Mettre à false pour utiliser l'API réelle via apiService

// Stockage local pour les données mock avec persistance via localStorage
const getStoredData = (key: string, defaultValue: any) => {
  try {
    const storedData = localStorage.getItem(`mock_${key}`);
    return storedData ? JSON.parse(storedData) : defaultValue;
  } catch (error) {
    console.error(`Error retrieving mock_${key} from localStorage:`, error);
    return defaultValue;
  }
};

const setStoredData = (key: string, value: any) => {
  try {
    localStorage.setItem(`mock_${key}`, JSON.stringify(value));
  } catch (error) {
    console.error(`Error storing mock_${key} in localStorage:`, error);
  }
};

// Valeurs par défaut pour les profils
const defaultProfiles = {
  clientProfile: {
    id: 1,
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phoneNumber: '+212 *********',
    address: 'Casablanca, Maroc',
    companyName: 'ABC Company',
    profileImage: 'https://randomuser.me/api/portraits/men/1.jpg',
  },
  coursierProfile: {
    id: 2,
    name: 'Ahmed Alami',
    email: '<EMAIL>',
    phoneNumber: '+212 *********',
    address: 'Rabat, Maroc',
    vehicleType: 'Moto',
    licenseNumber: 'AB123456',
    profileImage: 'https://randomuser.me/api/portraits/men/2.jpg',
  },
  entrepriseProfile: {
    id: 3,
    denomination: 'Charikti SARL',
    email: '<EMAIL>',
    phoneNumber: '+212 *********',
    siegeSocial: 'Casablanca, Maroc',
    formeJuridique: 'SARL',
    activite: 'Centre d\'affaire',
    nRC: '123456',
    nCnss: '789012',
    iFiscale: 'IF123456',
    taxProfessionel: 'TP789012',
    profileImage: 'https://randomuser.me/api/portraits/lego/1.jpg',
  },
  userProfile: {
    id: 1,
    name: 'Hamid',
    email: '<EMAIL>',
    role: 'client',
    profileImage: 'https://randomuser.me/api/portraits/men/1.jpg',
  },
  // Stockage des mots de passe des utilisateurs
  passwords: {
    '<EMAIL>': 'password123',
    '<EMAIL>': 'password123',
    '<EMAIL>': 'password123',
    '<EMAIL>': 'password123'
  }
};

// Initialiser le stockage avec les données du localStorage ou les valeurs par défaut
const mockStorage = {
  get clientProfile() { return getStoredData('clientProfile', defaultProfiles.clientProfile); },
  set clientProfile(value) { setStoredData('clientProfile', value); },

  get coursierProfile() { return getStoredData('coursierProfile', defaultProfiles.coursierProfile); },
  set coursierProfile(value) { setStoredData('coursierProfile', value); },

  get entrepriseProfile() { return getStoredData('entrepriseProfile', defaultProfiles.entrepriseProfile); },
  set entrepriseProfile(value) { setStoredData('entrepriseProfile', value); },

  get userProfile() { return getStoredData('userProfile', defaultProfiles.userProfile); },
  set userProfile(value) { setStoredData('userProfile', value); },

  get passwords() { return getStoredData('passwords', defaultProfiles.passwords); },
  set passwords(value) { setStoredData('passwords', value); },
};

// Créer une instance axios avec des configurations par défaut
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // Timeout de 10 secondes
});

// Intercepteur pour ajouter le token d'authentification à chaque requête
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les réponses et les erreurs
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error: AxiosError) => {
    if (error.response) {
      // La requête a été faite et le serveur a répondu avec un code d'état
      // qui n'est pas dans la plage 2xx
      console.error('Response error:', error.response.status, error.response.data);
    } else if (error.request) {
      // La requête a été faite mais aucune réponse n'a été reçue
      console.error('No response received:', error.request);
    } else {
      // Une erreur s'est produite lors de la configuration de la requête
      console.error('Request configuration error:', error.message);
    }
    return Promise.reject(error);
  }
);

// Fonction pour créer une réponse simulée
const mockResponse = <T>(data: T, delay = 500): Promise<AxiosResponse<T>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any, // Utiliser any pour éviter les problèmes de type
      });
    }, delay);
  });
};

// Wrapper pour les requêtes API avec support de mock
// Fonction utilitaire pour définir le rôle de l'utilisateur (pour les tests)
export const setUserRole = (role: 'client' | 'coursier' | 'entreprise') => {
  if (!mockStorage.userProfile) return;

  const oldRole = mockStorage.userProfile.role;

  // Récupérer l'image de profil stockée dans le localStorage
  let profileImage = '';
  const storageKey = `${role}_profile_image`;
  profileImage = localStorage.getItem(storageKey) || '';

  // Si aucune image n'est trouvée dans le localStorage, utiliser une image par défaut
  if (!profileImage) {
    switch (role) {
      case 'client':
        profileImage = 'https://randomuser.me/api/portraits/men/1.jpg';
        break;
      case 'coursier':
        profileImage = 'https://randomuser.me/api/portraits/men/2.jpg';
        break;
      case 'entreprise':
        profileImage = 'https://randomuser.me/api/portraits/lego/1.jpg';
        break;
    }
  }

  console.log(`Récupération de l'image depuis localStorage[${storageKey}]:`, profileImage ? 'Image trouvée' : 'Aucune image');

  // Mettre à jour les informations de l'utilisateur en fonction du nouveau rôle
  if (role === 'client' && mockStorage.clientProfile) {
    mockStorage.userProfile = {
      ...mockStorage.userProfile,
      role,
      name: `${mockStorage.clientProfile.firstName} ${mockStorage.clientProfile.lastName}`,
      email: mockStorage.clientProfile.email,
      profileImage: profileImage || mockStorage.clientProfile.profileImage
    };

    // Mettre à jour également l'image dans le profil client
    if (profileImage) {
      mockStorage.clientProfile = {
        ...mockStorage.clientProfile,
        profileImage: profileImage
      };
    }
  } else if (role === 'coursier' && mockStorage.coursierProfile) {
    mockStorage.userProfile = {
      ...mockStorage.userProfile,
      role,
      name: mockStorage.coursierProfile.name,
      email: mockStorage.coursierProfile.email,
      profileImage: profileImage || mockStorage.coursierProfile.profileImage
    };

    // Mettre à jour également l'image dans le profil coursier
    if (profileImage) {
      mockStorage.coursierProfile = {
        ...mockStorage.coursierProfile,
        profileImage: profileImage
      };
    }
  } else if (role === 'entreprise' && mockStorage.entrepriseProfile) {
    mockStorage.userProfile = {
      ...mockStorage.userProfile,
      role,
      name: mockStorage.entrepriseProfile.denomination,
      email: mockStorage.entrepriseProfile.email,
      profileImage: profileImage || mockStorage.entrepriseProfile.profileImage
    };

    // Mettre à jour également l'image dans le profil entreprise
    if (profileImage) {
      mockStorage.entrepriseProfile = {
        ...mockStorage.entrepriseProfile,
        profileImage: profileImage
      };
    }
  }

  console.log(`Rôle utilisateur changé de ${oldRole} à ${role}`);
  console.log('Nouveau profil utilisateur:', mockStorage.userProfile);
};

const apiWrapper = {
  get: async <T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    if (USE_MOCK) {
      // Gérer les mocks pour différents endpoints
      if (url.includes('/client/profile')) {
        // Les données sont déjà chargées depuis localStorage via les getters
        console.log('GET client profile:', mockStorage.clientProfile);
        return mockResponse(mockStorage.clientProfile as T);
      } else if (url.includes('/coursier/profile')) {
        // Les données sont déjà chargées depuis localStorage via les getters
        console.log('GET coursier profile:', mockStorage.coursierProfile);
        return mockResponse(mockStorage.coursierProfile as T);
      } else if (url.includes('/entreprise/profile')) {
        // Les données sont déjà chargées depuis localStorage via les getters
        console.log('GET entreprise profile:', mockStorage.entrepriseProfile);
        return mockResponse(mockStorage.entrepriseProfile as T);
      } else if (url.includes('/user')) {
        console.log('GET user profile:', mockStorage.userProfile);
        return mockResponse(mockStorage.userProfile as T);
      }

      // Mock par défaut
      return mockResponse({} as T);
    }

    // Utiliser l'API réelle via apiService
    try {
      if (url.includes('/client/profile')) {
        const profile = await apiService.client.getProfile();
        return { data: profile } as AxiosResponse<T>;
      } else if (url.includes('/coursier/profile')) {
        const profile = await apiService.coursier.getProfile();
        return { data: profile } as AxiosResponse<T>;
      } else if (url.includes('/entreprise/profile')) {
        const profile = await apiService.entreprise.getProfile();
        return { data: profile } as AxiosResponse<T>;
      } else if (url.includes('/user')) {
        const user = await apiService.auth.getUser();
        return { data: user } as AxiosResponse<T>;
      }

      // Fallback to regular API call
      return api.get(url, config);
    } catch (error) {
      console.error('API error:', error);
      throw error;
    }
  },

  post: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    if (USE_MOCK) {
      // Gérer les mocks pour différents endpoints
      if (url.includes('/login')) {
        // Vérifier les identifiants
        const { email, password } = data;

        // Vérifier que les identifiants sont valides
        if (!email || !password) {
          return Promise.reject({
            response: {
              status: 400,
              data: { message: 'Email et mot de passe requis' }
            }
          });
        }

        // Vérifier que le mot de passe est correct (dans un environnement réel, il serait haché)
        // Récupérer le mot de passe stocké pour cet email
        const storedPassword = mockStorage.passwords[email];

        if (!storedPassword || password !== storedPassword) {
          return Promise.reject({
            response: {
              status: 401,
              data: { message: 'Identifiants incorrects' }
            }
          });
        }

        // Déterminer le rôle en fonction de l'email
        let role: 'client' | 'coursier' | 'entreprise' = 'client';
        if (email.includes('coursier')) {
          role = 'coursier';
        } else if (email.includes('entreprise')) {
          role = 'entreprise';
        }

        // Mettre à jour le rôle de l'utilisateur
        setUserRole(role);

        // Générer un token aléatoire pour simuler l'authentification
        const token = 'mock-token-' + Math.random().toString(36).substring(2, 15);

        return mockResponse({
          token: token,
          user: mockStorage.userProfile
        } as T);
      } else if (url.includes('/client/profile/image')) {
        // Utiliser l'image réelle téléchargée par l'utilisateur
        // Extraire le fichier du FormData
        let imageUrl = '';

        if (data instanceof FormData && data.get('image') instanceof File) {
          const file = data.get('image') as File;

          // Convertir l'image en base64 pour pouvoir la stocker dans localStorage
          const reader = new FileReader();
          reader.onload = (event) => {
            if (event.target && event.target.result) {
              const base64Image = event.target.result as string;

              // Stocker l'image en base64 dans le localStorage
              try {
                localStorage.setItem('client_profile_image', base64Image);
                console.log('Image client stockée en base64 dans localStorage');
              } catch (error) {
                console.error('Erreur lors du stockage de l\'image dans localStorage:', error);
              }
            }
          };
          reader.readAsDataURL(file);

          // Créer une URL temporaire pour l'image téléchargée
          imageUrl = URL.createObjectURL(file);

          // Récupérer l'image stockée précédemment si disponible
          const storedImage = localStorage.getItem('client_profile_image');
          if (storedImage && storedImage.startsWith('data:image')) {
            imageUrl = storedImage;
          }
        } else {
          // Fallback si aucun fichier n'est trouvé
          const timestamp = new Date().getTime();
          imageUrl = localStorage.getItem('client_profile_image') ||
                    `https://randomuser.me/api/portraits/men/1.jpg?t=${timestamp}`;
        }

        // Mettre à jour le profil client avec la nouvelle image
        const updatedClientProfile = { ...mockStorage.clientProfile, profileImage: imageUrl };
        mockStorage.clientProfile = updatedClientProfile;

        // Mettre à jour également l'image de l'utilisateur si c'est un client
        if (mockStorage.userProfile.role === 'client') {
          mockStorage.userProfile = { ...mockStorage.userProfile, profileImage: imageUrl };
        }

        console.log('Image de profil client mise à jour:', imageUrl);

        // Forcer un délai pour simuler le téléchargement
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({
              data: { imageUrl } as unknown as T,
              status: 200,
              statusText: 'OK',
              headers: {},
              config: {} as any,
            });
          }, 1000);
        }) as Promise<AxiosResponse<T>>;
      } else if (url.includes('/coursier/profile/image')) {
        // Utiliser l'image réelle téléchargée par l'utilisateur
        // Extraire le fichier du FormData
        let imageUrl = '';

        if (data instanceof FormData && data.get('image') instanceof File) {
          const file = data.get('image') as File;

          // Convertir l'image en base64 pour pouvoir la stocker dans localStorage
          const reader = new FileReader();
          reader.onload = (event) => {
            if (event.target && event.target.result) {
              const base64Image = event.target.result as string;

              // Stocker l'image en base64 dans le localStorage
              try {
                localStorage.setItem('coursier_profile_image', base64Image);
                console.log('Image coursier stockée en base64 dans localStorage');
              } catch (error) {
                console.error('Erreur lors du stockage de l\'image dans localStorage:', error);
              }
            }
          };
          reader.readAsDataURL(file);

          // Créer une URL temporaire pour l'image téléchargée
          imageUrl = URL.createObjectURL(file);

          // Récupérer l'image stockée précédemment si disponible
          const storedImage = localStorage.getItem('coursier_profile_image');
          if (storedImage && storedImage.startsWith('data:image')) {
            imageUrl = storedImage;
          }
        } else {
          // Fallback si aucun fichier n'est trouvé
          const timestamp = new Date().getTime();
          imageUrl = localStorage.getItem('coursier_profile_image') ||
                    `https://randomuser.me/api/portraits/men/2.jpg?t=${timestamp}`;
        }

        // Mettre à jour le profil coursier avec la nouvelle image
        const updatedCoursierProfile = { ...mockStorage.coursierProfile, profileImage: imageUrl };
        mockStorage.coursierProfile = updatedCoursierProfile;

        // Mettre à jour également l'image de l'utilisateur si c'est un coursier
        if (mockStorage.userProfile.role === 'coursier') {
          mockStorage.userProfile = { ...mockStorage.userProfile, profileImage: imageUrl };
        }

        console.log('Image de profil coursier mise à jour:', imageUrl);

        // Forcer un délai pour simuler le téléchargement
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({
              data: { imageUrl } as unknown as T,
              status: 200,
              statusText: 'OK',
              headers: {},
              config: {} as any,
            });
          }, 1000);
        }) as Promise<AxiosResponse<T>>;
      } else if (url.includes('/entreprise/profile/image')) {
        // Utiliser l'image réelle téléchargée par l'utilisateur
        // Extraire le fichier du FormData
        let imageUrl = '';

        if (data instanceof FormData && data.get('image') instanceof File) {
          const file = data.get('image') as File;

          // Convertir l'image en base64 pour pouvoir la stocker dans localStorage
          const reader = new FileReader();
          reader.onload = (event) => {
            if (event.target && event.target.result) {
              const base64Image = event.target.result as string;

              // Stocker l'image en base64 dans le localStorage
              try {
                localStorage.setItem('entreprise_profile_image', base64Image);
                console.log('Image entreprise stockée en base64 dans localStorage');
              } catch (error) {
                console.error('Erreur lors du stockage de l\'image dans localStorage:', error);
              }
            }
          };
          reader.readAsDataURL(file);

          // Créer une URL temporaire pour l'image téléchargée
          imageUrl = URL.createObjectURL(file);

          // Récupérer l'image stockée précédemment si disponible
          const storedImage = localStorage.getItem('entreprise_profile_image');
          if (storedImage && storedImage.startsWith('data:image')) {
            imageUrl = storedImage;
          }
        } else {
          // Fallback si aucun fichier n'est trouvé
          const timestamp = new Date().getTime();
          imageUrl = localStorage.getItem('entreprise_profile_image') ||
                    `https://randomuser.me/api/portraits/lego/1.jpg?t=${timestamp}`;
        }

        // Mettre à jour le profil entreprise avec la nouvelle image
        const updatedEntrepriseProfile = { ...mockStorage.entrepriseProfile, profileImage: imageUrl };
        mockStorage.entrepriseProfile = updatedEntrepriseProfile;

        // Mettre à jour également l'image de l'utilisateur si c'est une entreprise
        if (mockStorage.userProfile.role === 'entreprise') {
          mockStorage.userProfile = { ...mockStorage.userProfile, profileImage: imageUrl };
        }

        console.log('Image de profil entreprise mise à jour:', imageUrl);

        // Forcer un délai pour simuler le téléchargement
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({
              data: { imageUrl } as unknown as T,
              status: 200,
              statusText: 'OK',
              headers: {},
              config: {} as any,
            });
          }, 1000);
        }) as Promise<AxiosResponse<T>>;
      }

      // Mock par défaut
      return mockResponse(data as T);
    }

    // Utiliser l'API réelle via apiService
    try {
      if (url.includes('/login')) {
        const { email, password } = data;
        const response = await apiService.auth.login({ email, password });
        return { data: response } as AxiosResponse<T>;
      } else if (url.includes('/client/profile/image') && data instanceof FormData) {
        const clientId = parseInt(url.split('/')[2], 10);
        const file = data.get('image') as File;
        if (file) {
          const response = await apiService.client.updateProfileImage(clientId, file);
          return { data: response } as AxiosResponse<T>;
        }
      } else if (url.includes('/coursier/profile/image') && data instanceof FormData) {
        const coursierId = parseInt(url.split('/')[2], 10);
        const file = data.get('image') as File;
        if (file) {
          const response = await apiService.coursier.updateProfileImage(coursierId, file);
          return { data: response } as AxiosResponse<T>;
        }
      } else if (url.includes('/entreprise/profile/image') && data instanceof FormData) {
        const entrepriseId = parseInt(url.split('/')[2], 10);
        const file = data.get('image') as File;
        if (file) {
          const response = await apiService.entreprise.updateProfileImage(entrepriseId, file);
          return { data: response } as AxiosResponse<T>;
        }
      }

      // Fallback to regular API call
      return api.post(url, data, config);
    } catch (error) {
      console.error('API error:', error);
      throw error;
    }
  },

  put: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    if (USE_MOCK) {
      // Simuler la mise à jour du profil
      if (url.includes('/client/profile')) {
        // Mettre à jour le profil client dans le stockage local
        const updatedProfile = { ...mockStorage.clientProfile, ...data };
        mockStorage.clientProfile = updatedProfile;
        console.log('Profil client mis à jour:', updatedProfile);

        // Si le rôle de l'utilisateur est client, mettre à jour également certaines informations dans le profil utilisateur
        if (mockStorage.userProfile.role === 'client') {
          mockStorage.userProfile = {
            ...mockStorage.userProfile,
            name: `${updatedProfile.firstName} ${updatedProfile.lastName}`,
            email: updatedProfile.email
          };
        }

        return mockResponse(updatedProfile as T, 800);
      } else if (url.includes('/coursier/profile')) {
        // Mettre à jour le profil coursier dans le stockage local
        const updatedProfile = { ...mockStorage.coursierProfile, ...data };
        mockStorage.coursierProfile = updatedProfile;
        console.log('Profil coursier mis à jour:', updatedProfile);

        // Si le rôle de l'utilisateur est coursier, mettre à jour également certaines informations dans le profil utilisateur
        if (mockStorage.userProfile.role === 'coursier') {
          mockStorage.userProfile = {
            ...mockStorage.userProfile,
            name: updatedProfile.name,
            email: updatedProfile.email
          };
        }

        return mockResponse(updatedProfile as T, 800);
      } else if (url.includes('/entreprise/profile')) {
        // Mettre à jour le profil entreprise dans le stockage local
        const updatedProfile = { ...mockStorage.entrepriseProfile, ...data };
        mockStorage.entrepriseProfile = updatedProfile;
        console.log('Profil entreprise mis à jour:', updatedProfile);

        // Si le rôle de l'utilisateur est entreprise, mettre à jour également certaines informations dans le profil utilisateur
        if (mockStorage.userProfile.role === 'entreprise') {
          mockStorage.userProfile = {
            ...mockStorage.userProfile,
            name: updatedProfile.denomination,
            email: updatedProfile.email
          };
        }

        return mockResponse(updatedProfile as T, 800);
      } else if (url.includes('/user/profile-image')) {
        // Mettre à jour l'image de profil de l'utilisateur
        const updatedProfile = { ...mockStorage.userProfile, ...data };
        mockStorage.userProfile = updatedProfile;
        console.log('Image de profil utilisateur mise à jour:', updatedProfile);
        return mockResponse(updatedProfile as T, 800);
      } else if (url.includes('/user/change-password')) {
        // Changer le mot de passe de l'utilisateur
        const { oldPassword, newPassword, email } = data;

        // Vérifier que l'ancien mot de passe est correct
        const storedPassword = mockStorage.passwords[email];
        if (!storedPassword || oldPassword !== storedPassword) {
          return Promise.reject({
            response: {
              status: 401,
              data: { message: 'Ancien mot de passe incorrect' }
            }
          });
        }

        // Mettre à jour le mot de passe
        mockStorage.passwords = {
          ...mockStorage.passwords,
          [email]: newPassword
        };

        console.log(`Mot de passe changé pour ${email}`);
        return mockResponse({ success: true } as T, 800);
      }

      // Mock par défaut
      return mockResponse(data as T);
    }

    // Utiliser l'API réelle via apiService
    try {
      if (url.includes('/client/profile')) {
        const clientId = parseInt(url.split('/')[2], 10);
        const response = await apiService.client.update(clientId, data);
        return { data: response } as AxiosResponse<T>;
      } else if (url.includes('/coursier/profile')) {
        const coursierId = parseInt(url.split('/')[2], 10);
        const response = await apiService.coursier.update(coursierId, data);
        return { data: response } as AxiosResponse<T>;
      } else if (url.includes('/entreprise/profile')) {
        const entrepriseId = parseInt(url.split('/')[2], 10);
        const response = await apiService.entreprise.update(entrepriseId, data);
        return { data: response } as AxiosResponse<T>;
      } else if (url.includes('/user/change-password')) {
        const response = await apiService.auth.changePassword(data);
        return { data: response } as AxiosResponse<T>;
      }

      // Fallback to regular API call
      return api.put(url, data, config);
    } catch (error) {
      console.error('API error:', error);
      throw error;
    }
  },

  delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    if (USE_MOCK) {
      // Mock par défaut
      return mockResponse({} as T);
    }

    // Utiliser l'API réelle via apiService
    try {
      if (url.includes('/client/')) {
        const clientId = parseInt(url.split('/')[2], 10);
        const response = await apiService.client.delete(clientId);
        return { data: response } as AxiosResponse<T>;
      } else if (url.includes('/coursier/')) {
        const coursierId = parseInt(url.split('/')[2], 10);
        const response = await apiService.coursier.delete(coursierId);
        return { data: response } as AxiosResponse<T>;
      } else if (url.includes('/entreprise/')) {
        const entrepriseId = parseInt(url.split('/')[2], 10);
        const response = await apiService.entreprise.delete(entrepriseId);
        return { data: response } as AxiosResponse<T>;
      }

      // Fallback to regular API call
      return api.delete(url, config);
    } catch (error) {
      console.error('API error:', error);
      throw error;
    }
  }
};

export default apiWrapper;
