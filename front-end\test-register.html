<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Register API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Test Register API</h1>

    <div class="form-group">
        <label for="name">Name:</label>
        <input type="text" id="name" value="Test User">
    </div>

    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>

    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="password123">
    </div>

    <div class="form-group">
        <label for="password_confirmation">Confirm Password:</label>
        <input type="password" id="password_confirmation" value="password123">
    </div>

    <div class="form-group">
        <label for="role">Role:</label>
        <select id="role">
            <option value="client">Client</option>
            <option value="coursier">Coursier</option>
            <option value="entreprise">Entreprise</option>
            <option value="admin">Administrateur</option>
        </select>
    </div>

    <button onclick="testRegister()">Test Register</button>

    <div id="result"></div>

    <script>
        async function testRegister() {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = 'Sending request...';

            const registerData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                password_confirmation: document.getElementById('password_confirmation').value,
                role: document.getElementById('role').value
            };

            try {
                const response = await fetch('http://localhost:8000/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(registerData),
                    mode: 'cors',
                    credentials: 'include'
                });

                const data = await response.json();

                resultDiv.textContent = 'Status: ' + response.status + '\n\n' + JSON.stringify(data, null, 2);

                if (response.ok) {
                    // Store token in localStorage
                    if (data.token) {
                        localStorage.setItem('token', data.token);
                        resultDiv.textContent += '\n\nToken stored in localStorage';
                    }
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                console.error('Error:', error);
            }
        }
    </script>
</body>
</html>
