import api from './api';
import { handleApiError } from './errorHandler';

// Interface pour le changement de mot de passe
interface ChangePasswordData {
  email: string;
  oldPassword: string;
  newPassword: string;
}

// Service d'authentification
export const authService = {
  // Changer le mot de passe de l'utilisateur
  changePassword: async (data: ChangePasswordData): Promise<boolean> => {
    try {
      const response = await api.put<{ success: boolean }>('/user/change-password', data);
      return response.data.success;
    } catch (error) {
      // Gérer l'erreur mais ne pas la lancer à nouveau
      console.error('Erreur lors du changement de mot de passe:', error);

      // Extraire un message d'erreur plus spécifique si disponible
      let errorMessage = 'Erreur lors du changement de mot de passe';
      if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Lancer l'erreur avec le message approprié
      throw new Error(errorMessage);
    }
  },
};
